<!DOCTYPE html>
<html dir="rtl" lang="ar" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجموعة ميثاق للأعمال والاستثمارات الهندسية - MYTHAQ Engineering Works & Investments</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏗️</text></svg>">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-gold: #CBA135;
            --secondary-gold: #D4AF37;
            --dark-gold: #B8941F;
            --primary-blue: #1e3c72;
            --secondary-blue: #2a5298;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .lang-en {
            font-family: 'Inter', Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .gold {
            color: var(--primary-gold);
        }
        
        .text-center {
            text-align: center;
        }
        
        /* Navigation */
        .navbar {
            background: rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .nav-brand h2 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .lang-btn {
            background: rgba(203,161,53,0.2);
            color: var(--primary-gold);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            border: 2px solid var(--primary-gold);
            transition: all 0.3s ease;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .lang-btn:hover {
            background: var(--primary-gold);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(203,161,53,0.3);
        }
        
        /* Hero Section */
        .hero {
            padding: 140px 0 100px;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(203,161,53,0.1) 0%, transparent 70%);
            pointer-events: none;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-title {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            font-weight: 700;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
            animation: fadeInUp 1s ease-out;
        }
        
        .hero-subtitle {
            font-size: 1.8rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            font-style: italic;
            animation: fadeInUp 1s ease-out 0.2s both;
        }
        
        .hero-description {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
            animation: fadeInUp 1s ease-out 0.4s both;
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Sections */
        section {
            padding: 80px 0;
        }
        
        .about {
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .section-title {
            font-size: 3rem;
            margin-bottom: 3rem;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .section-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            align-items: center;
        }
        
        .lead {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            color: rgba(255,255,255,0.95);
            line-height: 1.8;
        }
        
        .about p {
            color: rgba(255,255,255,0.85);
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            line-height: 1.7;
        }
        
        .mission {
            background: linear-gradient(135deg, rgba(203,161,53,0.15), rgba(212,175,55,0.1));
            padding: 30px;
            border-radius: 15px;
            border-right: 5px solid var(--primary-gold);
            margin-top: 2.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .mission h3 {
            margin-bottom: 1.5rem;
            font-size: 1.4rem;
        }
        
        /* Founder Card */
        .founder-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            padding: 40px;
            border-radius: 25px;
            text-align: center;
            border: 2px solid rgba(203,161,53,0.3);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .founder-card:hover {
            transform: translateY(-5px);
        }
        
        .founder-image {
            margin-bottom: 25px;
        }
        
        .founder-placeholder {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border: 4px solid rgba(203,161,53,0.5);
            box-shadow: 0 10px 25px rgba(203,161,53,0.3);
        }
        
        .founder-icon {
            font-size: 4rem;
            color: white;
        }
        
        .founder-name {
            font-size: 1.5rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
        }
        
        .founder-title {
            color: rgba(255,255,255,0.85);
            margin-bottom: 1.5rem;
            font-style: italic;
            font-size: 1.1rem;
        }
        
        .founder-desc {
            color: rgba(255,255,255,0.8);
            font-size: 1rem;
            line-height: 1.6;
        }
        
        /* Services */
        .services {
            background: rgba(0,0,0,0.1);
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 4rem;
        }
        
        .service-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            padding: 35px 25px;
            border-radius: 20px;
            text-align: center;
            border: 2px solid rgba(203,161,53,0.3);
            transition: all 0.3s ease;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .service-card:hover {
            transform: translateY(-8px);
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .service-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .service-card h3 {
            color: var(--primary-gold);
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        /* Contact */
        .contact {
            background: rgba(255,255,255,0.05);
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 4rem;
        }
        
        .contact-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            border: 2px solid rgba(203,161,53,0.3);
            transition: transform 0.3s ease;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
        }
        
        .contact-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .contact-card h3 {
            color: var(--primary-gold);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .contact-card p {
            color: rgba(255,255,255,0.85);
            font-size: 1.1rem;
        }
        
        /* Footer */
        .footer {
            background: rgba(0,0,0,0.3);
            padding: 40px 0;
            text-align: center;
            color: rgba(255,255,255,0.8);
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        
        .footer-content p {
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }
        
        /* WhatsApp Button */
        .whatsapp-btn {
            position: fixed;
            bottom: 25px;
            right: 25px;
            z-index: 1000;
        }
        
        .whatsapp-btn a {
            display: flex;
            align-items: center;
            background: #25D366;
            color: white;
            padding: 15px 25px;
            border-radius: 30px;
            text-decoration: none;
            box-shadow: 0 8px 25px rgba(37,211,102,0.4);
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .whatsapp-btn a:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(37,211,102,0.5);
            background: #20c55a;
        }
        
        .whatsapp-icon {
            margin-right: 10px;
            font-size: 1.4rem;
        }
        
        /* Language Toggle */
        .content-ar, .content-en {
            display: none;
        }
        
        .content-ar.active, .content-en.active {
            display: block;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.8rem;
            }
            
            .hero-subtitle {
                font-size: 1.4rem;
            }
            
            .section-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .services-grid,
            .contact-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-container {
                flex-direction: column;
                gap: 1rem;
                padding: 15px 20px;
            }
            
            .hero {
                padding: 120px 0 80px;
            }
            
            .section-title {
                font-size: 2.2rem;
            }
        }
        
        /* English Direction */
        .lang-en {
            direction: ltr;
        }
        
        .lang-en .mission {
            border-right: none;
            border-left: 5px solid var(--primary-gold);
        }
        
        .lang-en .whatsapp-btn {
            right: auto;
            left: 25px;
        }
        
        .lang-en .whatsapp-icon {
            margin-right: 0;
            margin-left: 10px;
        }
        
        /* Success indicator */
        .success-indicator {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(40,167,69,0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 2000;
            animation: slideDown 0.5s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }
    </style>
</head>
<body id="body">
    <!-- Success Indicator -->
    <div class="success-indicator">
        🎉 الموقع يعمل بنجاح! Website is working successfully!
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h2 class="gold" id="nav-title">ميثاق</h2>
            </div>
            <div class="nav-links">
                <button class="lang-btn" onclick="toggleLanguage()" id="lang-toggle">English</button>
            </div>
        </div>
    </nav>

    <!-- Arabic Content -->
    <div class="content-ar active" id="content-ar">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="gold">ميثاق</span><br>
                        للأعمال والاستثمارات الهندسية
                    </h1>
                    <p class="hero-subtitle">رؤية تُبنى. وفكرة تعيش.</p>
                    <p class="hero-description">
                        مجموعة ميثاق للأعمال والاستثمارات الهندسية<br>
                        نحول أحلامكم المعمارية إلى واقع مبهر بخبرة وإبداع لا محدود
                    </p>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about">
            <div class="container">
                <div class="section-grid">
                    <div class="content-side">
                        <h2 class="section-title gold">من نحن</h2>
                        <p class="lead">
                            تأسست مجموعة ميثاق للأعمال والاستثمارات الهندسية برؤية واضحة لتقديم حلول هندسية ومعمارية استثنائية في ليبيا، وتحديداً في مدينة مصراتة.
                        </p>
                        <p>
                            تختص مجموعتنا في التصميم المعماري والإشراف على المشاريع والترميم والاستشارات الهندسية الشاملة والمشاريع الاستثمارية. نحن ملتزمون بتحويل أحلام عملائنا إلى واقع معماري متميز من خلال خبرة وإبداع لا محدود.
                        </p>
                        <div class="mission">
                            <h3 class="gold">رسالتنا:</h3>
                            <p>أن نكون المجموعة الرائدة في الأعمال والاستثمارات الهندسية في ليبيا، نقدم حلولاً مبتكرة تفوق التوقعات وتساهم في بناء مستقبل أفضل لمجتمعنا.</p>
                        </div>
                    </div>

                    <div class="founder-side">
                        <div class="founder-card">
                            <div class="founder-image">
                                <div class="founder-placeholder">
                                    <span class="founder-icon">👤</span>
                                </div>
                            </div>
                            <div class="founder-info">
                                <h3 class="founder-name gold">م. علي عمر باكير ابورقيبه</h3>
                                <p class="founder-title">المؤسس والمدير التنفيذي</p>
                                <p class="founder-desc">
                                    بسنوات من الخبرة في الهندسة والعمارة، أسس المهندس علي عمر باكير ابورقيبه مجموعة ميثاق لتقديم حلول مبتكرة وجودة استثنائية لصناعة البناء والاستثمار في ليبيا، وخاصة لخدمة منطقة مصراتة.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services">
            <div class="container">
                <h2 class="section-title gold text-center">خدماتنا المتميزة</h2>
                <div class="services-grid">
                    <div class="service-card">
                        <span class="service-icon">🏗️</span>
                        <h3>التصميم المعماري والداخلي</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">👷</span>
                        <h3>الإشراف والتنفيذ</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">🔧</span>
                        <h3>الترميم والصيانة</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">💡</span>
                        <h3>الاستشارات الهندسية والاستثمارية</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">🏢</span>
                        <h3>تطوير المشاريع السكنية والتجارية</h3>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact">
            <div class="container">
                <h2 class="section-title gold text-center">تواصل معنا</h2>
                <div class="contact-grid">
                    <div class="contact-card">
                        <span class="contact-icon">📍</span>
                        <h3>العنوان</h3>
                        <p>ليبيا، مصراتة، المقاوبة</p>
                    </div>
                    <div class="contact-card">
                        <span class="contact-icon">📞</span>
                        <h3>الهاتف</h3>
                        <p>+218 945446851</p>
                    </div>
                    <div class="contact-card">
                        <span class="contact-icon">✉️</span>
                        <h3>البريد الإلكتروني</h3>
                        <p><EMAIL></p>
                    </div>
                    <div class="contact-card">
                        <span class="contact-icon">🕒</span>
                        <h3>ساعات العمل</h3>
                        <p>الأحد - الخميس: 8:00 ص - 6:00 م</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <p>© 2025 مجموعة ميثاق للأعمال والاستثمارات الهندسية - جميع الحقوق محفوظة</p>
                    <p>تصميم وتطوير: فريق ميثاق التقني</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- English Content -->
    <div class="content-en" id="content-en">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="gold">MYTHAQ</span><br>
                        Engineering Works & Investments
                    </h1>
                    <p class="hero-subtitle">A Vision Built. An Idea That Lives.</p>
                    <p class="hero-description">
                        MYTHAQ Engineering Works & Investments Group<br>
                        We transform your architectural dreams into stunning reality with unlimited expertise and creativity
                    </p>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about">
            <div class="container">
                <div class="section-grid">
                    <div class="content-side">
                        <h2 class="section-title gold">About Us</h2>
                        <p class="lead">
                            MYTHAQ Engineering Works & Investments Group was founded with a vision to provide exceptional engineering and architectural solutions in Libya, specifically in Misrata.
                        </p>
                        <p>
                            Our group specializes in architectural design, project supervision, renovation, comprehensive engineering consultancy, and investment projects. We are committed to transforming our clients' dreams into outstanding architectural reality through unlimited expertise and creativity.
                        </p>
                        <div class="mission">
                            <h3 class="gold">Our Mission:</h3>
                            <p>To be the leading engineering works and investments group in Libya, delivering innovative solutions that exceed expectations and contribute to building a better future for our community.</p>
                        </div>
                    </div>

                    <div class="founder-side">
                        <div class="founder-card">
                            <div class="founder-image">
                                <div class="founder-placeholder">
                                    <span class="founder-icon">👤</span>
                                </div>
                            </div>
                            <div class="founder-info">
                                <h3 class="founder-name gold">Eng. Ali Omar Bakir Aburqaiba</h3>
                                <p class="founder-title">Founder & CEO</p>
                                <p class="founder-desc">
                                    With years of experience in engineering and architecture, Eng. Ali Omar Bakir Aburqaiba established MYTHAQ to bring innovative solutions and exceptional quality to the construction and investment industry in Libya, specifically serving the Misrata region.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services">
            <div class="container">
                <h2 class="section-title gold text-center">Our Distinguished Services</h2>
                <div class="services-grid">
                    <div class="service-card">
                        <span class="service-icon">🏗️</span>
                        <h3>Architectural & Interior Design</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">👷</span>
                        <h3>Supervision & Implementation</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">🔧</span>
                        <h3>Renovation & Maintenance</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">💡</span>
                        <h3>Engineering & Investment Consultation</h3>
                    </div>
                    <div class="service-card">
                        <span class="service-icon">🏢</span>
                        <h3>Residential & Commercial Development</h3>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact">
            <div class="container">
                <h2 class="section-title gold text-center">Contact Us</h2>
                <div class="contact-grid">
                    <div class="contact-card">
                        <span class="contact-icon">📍</span>
                        <h3>Address</h3>
                        <p>Libya, Misrata, Al-Muqawaba</p>
                    </div>
                    <div class="contact-card">
                        <span class="contact-icon">📞</span>
                        <h3>Phone</h3>
                        <p>+218 945446851</p>
                    </div>
                    <div class="contact-card">
                        <span class="contact-icon">✉️</span>
                        <h3>Email</h3>
                        <p><EMAIL></p>
                    </div>
                    <div class="contact-card">
                        <span class="contact-icon">🕒</span>
                        <h3>Working Hours</h3>
                        <p>Sunday - Thursday: 8:00 AM - 6:00 PM</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <p>© 2025 MYTHAQ Engineering Works & Investments - All Rights Reserved</p>
                    <p>Design & Development: MYTHAQ Tech Team</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- WhatsApp Button -->
    <div class="whatsapp-btn">
        <a href="https://wa.me/218945446851" target="_blank" id="whatsapp-link">
            <span class="whatsapp-icon">💬</span>
            <span class="whatsapp-text" id="whatsapp-text">واتساب</span>
        </a>
    </div>

    <script>
        let currentLang = 'ar';

        function toggleLanguage() {
            const body = document.getElementById('body');
            const htmlRoot = document.getElementById('html-root');
            const navTitle = document.getElementById('nav-title');
            const langToggle = document.getElementById('lang-toggle');
            const contentAr = document.getElementById('content-ar');
            const contentEn = document.getElementById('content-en');
            const whatsappText = document.getElementById('whatsapp-text');
            const whatsappLink = document.getElementById('whatsapp-link');

            if (currentLang === 'ar') {
                // Switch to English
                currentLang = 'en';
                body.className = 'lang-en';
                htmlRoot.dir = 'ltr';
                htmlRoot.lang = 'en';
                navTitle.textContent = 'MYTHAQ';
                langToggle.textContent = 'العربية';
                contentAr.classList.remove('active');
                contentEn.classList.add('active');
                whatsappText.textContent = 'WhatsApp';
                whatsappLink.title = 'Chat on WhatsApp';

                // Update page title
                document.title = 'MYTHAQ Engineering Works & Investments - مجموعة ميثاق للأعمال والاستثمارات الهندسية';
            } else {
                // Switch to Arabic
                currentLang = 'ar';
                body.className = '';
                htmlRoot.dir = 'rtl';
                htmlRoot.lang = 'ar';
                navTitle.textContent = 'ميثاق';
                langToggle.textContent = 'English';
                contentEn.classList.remove('active');
                contentAr.classList.add('active');
                whatsappText.textContent = 'واتساب';
                whatsappLink.title = 'تواصل عبر الواتساب';

                // Update page title
                document.title = 'مجموعة ميثاق للأعمال والاستثمارات الهندسية - MYTHAQ Engineering Works & Investments';
            }
        }

        // Hide success indicator after 3 seconds
        setTimeout(function() {
            const indicator = document.querySelector('.success-indicator');
            if (indicator) {
                indicator.style.animation = 'slideDown 0.5s ease-out reverse';
                setTimeout(() => indicator.remove(), 500);
            }
        }, 3000);

        // Console messages
        console.log('🎉 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية جاهز!');
        console.log('🎉 MYTHAQ Engineering Works & Investments website is ready!');
        console.log('📞 للتواصل: +218 945446851');
        console.log('📧 البريد الإلكتروني: <EMAIL>');
        console.log('🌐 الموقع يعمل بدون خادم - يمكن فتحه مباشرة في المتصفح');
        console.log('🌐 Website works without server - can be opened directly in browser');

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
