@echo off
chcp 65001 >nul
title موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية

echo.
echo ================================================================
echo   🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo   🚀 MYTHAQ Engineering Works & Investments Group
echo ================================================================
echo.

REM الحصول على المسار الكامل للمجلد
set "SCRIPT_DIR=%~dp0"
echo 📁 مجلد المشروع: %SCRIPT_DIR%
echo 📁 Project folder: %SCRIPT_DIR%
echo.

REM التحقق من وجود الملفات
echo 🔍 البحث عن ملفات الموقع...
echo 🔍 Looking for website files...

if exist "%SCRIPT_DIR%mythaq_final.html" (
    echo ✅ تم العثور على: mythaq_final.html
    echo ✅ Found: mythaq_final.html
    set "HTML_FILE=%SCRIPT_DIR%mythaq_final.html"
    goto :open_file
) else if exist "%SCRIPT_DIR%mythaq_website.html" (
    echo ✅ تم العثور على: mythaq_website.html
    echo ✅ Found: mythaq_website.html
    set "HTML_FILE=%SCRIPT_DIR%mythaq_website.html"
    goto :open_file
) else (
    echo ❌ لم يتم العثور على ملفات HTML
    echo ❌ HTML files not found
    goto :error
)

:open_file
echo.
echo 🌐 فتح الموقع...
echo 🌐 Opening website...
echo 📂 الملف: %HTML_FILE%
echo 📂 File: %HTML_FILE%
echo.

REM محاولة فتح الملف بطرق مختلفة
echo 🔄 محاولة الفتح بالمتصفح الافتراضي...
echo 🔄 Trying to open with default browser...
start "" "%HTML_FILE%"

if %errorlevel% neq 0 (
    echo ⚠️ فشل الفتح بالطريقة الأولى، جرب طريقة أخرى...
    echo ⚠️ First method failed, trying another way...
    
    REM جرب مع explorer
    explorer "%HTML_FILE%"
    
    if %errorlevel% neq 0 (
        echo ⚠️ فشل الفتح بالطريقة الثانية، جرب طريقة ثالثة...
        echo ⚠️ Second method failed, trying third way...
        
        REM جرب مع rundll32
        rundll32 url.dll,FileProtocolHandler "%HTML_FILE%"
    )
)

echo.
echo ✅ تم تنفيذ أمر الفتح
echo ✅ Open command executed
echo.
echo 📋 معلومات الموقع:
echo 📋 Website information:
echo    - الشركة: مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo    - Company: MYTHAQ Engineering Works & Investments
echo    - المؤسس: م. علي عمر باكير ابورقيبه
echo    - Founder: Eng. Ali Omar Bakir Aburqaiba
echo    - الهاتف: +218 *********
echo    - البريد: <EMAIL>
echo.
echo 💡 إذا لم يفتح الموقع تلقائياً:
echo 💡 If the website didn't open automatically:
echo    1. افتح المتصفح يدوياً
echo    1. Open browser manually
echo    2. اسحب الملف إلى المتصفح
echo    2. Drag the file to browser
echo    3. أو انسخ المسار: %HTML_FILE%
echo    3. Or copy the path: %HTML_FILE%
goto :end

:error
echo.
echo ❌ خطأ: لم يتم العثور على ملفات الموقع
echo ❌ Error: Website files not found
echo.
echo 📂 الملفات الموجودة في المجلد:
echo 📂 Files in the folder:
dir "%SCRIPT_DIR%*.html" /b 2>nul
if %errorlevel% neq 0 (
    echo    لا توجد ملفات HTML
    echo    No HTML files found
)
echo.
echo 💡 تأكد من وجود أحد الملفات التالية:
echo 💡 Make sure one of these files exists:
echo    - mythaq_final.html
echo    - mythaq_website.html
echo.

:end
echo.
echo 🎉 انتهى التشغيل
echo 🎉 Execution completed
echo.
pause
