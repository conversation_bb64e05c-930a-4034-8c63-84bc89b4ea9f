# 📧 تعليمات إعداد البريد الإلكتروني لموقع WEDYAR

## خطوات إعداد Gmail لإرسال الإيميلات:

### 1. تفعيل التحقق بخطوتين (Two-Factor Authentication):
1. اذه<PERSON> إلى [myaccount.google.com](https://myaccount.google.com)
2. انقر على "الأمان" (Security)
3. فعل "التحقق بخطوتين" (2-Step Verification)

### 2. إنشاء App Password:
1. في صفحة الأمان، انقر على "App passwords"
2. اختر "Mail" كنوع التطبيق
3. اختر "Other" واكتب "WEDYAR Website"
4. انسخ كلمة المرور المُنشأة (16 رقم)

### 3. إض<PERSON><PERSON>ة كلمة المرور للموقع:
1. ا<PERSON><PERSON><PERSON> ملف `app/__init__.py`
2. اب<PERSON><PERSON> عن السطر:
   ```python
   app.config['MAIL_PASSWORD'] = ''
   ```
3. ضع كلمة المرور بين العلامتين:
   ```python
   app.config['MAIL_PASSWORD'] = 'your_16_digit_app_password'
   ```

### 4. إضافة كلمة المرور للكود:
1. افتح ملف `app/routes.py`
2. ابحث عن السطر:
   ```python
   sender_password = ""  # ضع App Password هنا
   ```
3. ضع كلمة المرور بين العلامتين:
   ```python
   sender_password = "your_16_digit_app_password"
   ```

## ✅ اختبار النظام:
1. شغل الموقع
2. اذهب إلى صفحة "تواصل معنا"
3. املأ النموذج وأرسل رسالة تجريبية
4. تحقق من وصول الإيميل إلى <EMAIL>

## 🔧 استكشاف الأخطاء:
- تأكد من صحة App Password
- تأكد من تفعيل التحقق بخطوتين
- تحقق من اتصال الإنترنت
- راجع console للأخطاء

## 📋 ملاحظات:
- الإيميلات ستصل إلى: <EMAIL>
- النظام يدعم HTML و Plain Text
- يتم حفظ معلومات المرسل كاملة
- التاريخ والوقت يُسجل تلقائياً
