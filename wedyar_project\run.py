from app import create_app

app = create_app()

if __name__ == "__main__":
    print("🚀 بدء تشغيل موقع METAQ...")
    print("📍 الموقع متاح على: http://127.0.0.1:5000")
    print("⏹️  للإيقاف اضغط Ctrl+C")
    print("-" * 50)

    try:
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=True
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")
