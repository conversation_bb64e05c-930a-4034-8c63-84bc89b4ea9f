#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم ويب مستقل لموقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
Standalone web server for MYTHAQ Engineering Works & Investments
يعمل بدون مكتبات خارجية - يستخدم فقط مكتبات Python الأساسية
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from urllib.parse import urlparse, parse_qs

# إعداد المنفذ
PORT = 5000

class MythaqHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""

        # تحليل المسار
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # الصفحة الرئيسية
        if path == '/' or path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            html_content = self.get_main_page()
            self.wfile.write(html_content.encode('utf-8'))

        # ملف CSS
        elif path == '/style.css':
            self.send_response(200)
            self.send_header('Content-type', 'text/css; charset=utf-8')
            self.end_headers()

            css_content = self.get_css()
            self.wfile.write(css_content.encode('utf-8'))

        # تغيير اللغة
        elif path.startswith('/set_language/'):
            lang = path.split('/')[-1]
            self.send_response(302)
            self.send_header('Location', f'/?lang={lang}')
            self.end_headers()

        else:
            # ملفات أخرى
            super().do_GET()

    def get_main_page(self):
        """إنشاء الصفحة الرئيسية"""

        # تحديد اللغة
        parsed_path = urlparse(self.path)
        query_params = parse_qs(parsed_path.query)
        lang = query_params.get('lang', ['ar'])[0]

        if lang == 'en':
            return self.get_english_page()
        else:
            return self.get_arabic_page()

    def get_english_page(self):
        """الصفحة الإنجليزية"""
        return '''<!DOCTYPE html>
<html dir="ltr" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MYTHAQ Engineering Works & Investments</title>
    <link rel="stylesheet" href="/style.css">
</head>
<body class="lang-en">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h2 class="gold">MYTHAQ</h2>
            </div>
            <div class="nav-links">
                <a href="?lang=ar" class="lang-btn">العربية</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="gold">MYTHAQ</span><br>
                    Engineering Works & Investments
                </h1>
                <p class="hero-subtitle">A Vision Built. An Idea That Lives.</p>
                <p class="hero-description">
                    MYTHAQ Engineering Works & Investments Group<br>
                    We transform your architectural dreams into stunning reality with unlimited expertise and creativity
                </p>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about">
        <div class="container">
            <div class="section-grid">
                <div class="content-side">
                    <h2 class="section-title gold">About Us</h2>
                    <p class="lead">
                        MYTHAQ Engineering Works & Investments Group was founded with a vision to provide exceptional engineering and architectural solutions in Libya, specifically in Misrata.
                    </p>
                    <p>
                        Our group specializes in architectural design, project supervision, renovation, comprehensive engineering consultancy, and investment projects.
                    </p>
                    <div class="mission">
                        <h3 class="gold">Our Mission:</h3>
                        <p>To be the leading engineering works and investments group in Libya, delivering innovative solutions that exceed expectations.</p>
                    </div>
                </div>

                <div class="founder-side">
                    <div class="founder-card">
                        <div class="founder-image">
                            <div class="founder-placeholder">
                                <span class="founder-icon">👤</span>
                            </div>
                        </div>
                        <div class="founder-info">
                            <h3 class="founder-name gold">Eng. Ali Omar Bakir Aburqaiba</h3>
                            <p class="founder-title">Founder & CEO</p>
                            <p class="founder-desc">
                                With years of experience in engineering and architecture, Eng. Ali Omar Bakir Aburqaiba established MYTHAQ to bring innovative solutions and exceptional quality.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services">
        <div class="container">
            <h2 class="section-title gold text-center">Our Distinguished Services</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🏗️</div>
                    <h3>Architectural & Interior Design</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">👷</div>
                    <h3>Supervision & Implementation</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">🔧</div>
                    <h3>Renovation & Maintenance</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">💡</div>
                    <h3>Engineering & Investment Consultation</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">🏢</div>
                    <h3>Residential & Commercial Development</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title gold text-center">Contact Us</h2>
            <div class="contact-grid">
                <div class="contact-card">
                    <div class="contact-icon">📍</div>
                    <h3>Address</h3>
                    <p>Libya, Misrata, Al-Muqawaba</p>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">📞</div>
                    <h3>Phone</h3>
                    <p>+218 945446851</p>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">✉️</div>
                    <h3>Email</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">🕒</div>
                    <h3>Working Hours</h3>
                    <p>Sunday - Thursday: 8:00 AM - 6:00 PM</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>© 2025 MYTHAQ Engineering Works & Investments - All Rights Reserved</p>
                <p>Design & Development: MYTHAQ Tech Team</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <div class="whatsapp-btn">
        <a href="https://wa.me/218945446851" target="_blank" title="Chat on WhatsApp">
            <span class="whatsapp-icon">💬</span>
            <span class="whatsapp-text">WhatsApp</span>
        </a>
    </div>
</body>
</html>'''

    def get_arabic_page(self):
        """الصفحة العربية"""
        return '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجموعة ميثاق للأعمال والاستثمارات الهندسية</title>
    <link rel="stylesheet" href="/style.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h2 class="gold">ميثاق</h2>
            </div>
            <div class="nav-links">
                <a href="?lang=en" class="lang-btn">English</a>
            </div>
        </div>
    </nav>

    <!-- القسم الرئيسي -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="gold">ميثاق</span><br>
                    للأعمال والاستثمارات الهندسية
                </h1>
                <p class="hero-subtitle">رؤية تُبنى. وفكرة تعيش.</p>
                <p class="hero-description">
                    مجموعة ميثاق للأعمال والاستثمارات الهندسية<br>
                    نحول أحلامكم المعمارية إلى واقع مبهر بخبرة وإبداع لا محدود
                </p>
            </div>
        </div>
    </section>

    <!-- قسم من نحن -->
    <section class="about">
        <div class="container">
            <div class="section-grid">
                <div class="content-side">
                    <h2 class="section-title gold">من نحن</h2>
                    <p class="lead">
                        تأسست مجموعة ميثاق للأعمال والاستثمارات الهندسية برؤية واضحة لتقديم حلول هندسية ومعمارية استثنائية في ليبيا، وتحديداً في مدينة مصراتة.
                    </p>
                    <p>
                        تختص مجموعتنا في التصميم المعماري والإشراف على المشاريع والترميم والاستشارات الهندسية الشاملة والمشاريع الاستثمارية.
                    </p>
                    <div class="mission">
                        <h3 class="gold">رسالتنا:</h3>
                        <p>أن نكون المجموعة الرائدة في الأعمال والاستثمارات الهندسية في ليبيا، نقدم حلولاً مبتكرة تفوق التوقعات.</p>
                    </div>
                </div>

                <div class="founder-side">
                    <div class="founder-card">
                        <div class="founder-image">
                            <div class="founder-placeholder">
                                <span class="founder-icon">👤</span>
                            </div>
                        </div>
                        <div class="founder-info">
                            <h3 class="founder-name gold">م. علي عمر باكير ابورقيبه</h3>
                            <p class="founder-title">المؤسس والمدير التنفيذي</p>
                            <p class="founder-desc">
                                بسنوات من الخبرة في الهندسة والعمارة، أسس المهندس علي عمر باكير ابورقيبه مجموعة ميثاق لتقديم حلول مبتكرة وجودة استثنائية.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الخدمات -->
    <section class="services">
        <div class="container">
            <h2 class="section-title gold text-center">خدماتنا المتميزة</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🏗️</div>
                    <h3>التصميم المعماري والداخلي</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">👷</div>
                    <h3>الإشراف والتنفيذ</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">🔧</div>
                    <h3>الترميم والصيانة</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">💡</div>
                    <h3>الاستشارات الهندسية والاستثمارية</h3>
                </div>
                <div class="service-card">
                    <div class="service-icon">🏢</div>
                    <h3>تطوير المشاريع السكنية والتجارية</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الاتصال -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title gold text-center">تواصل معنا</h2>
            <div class="contact-grid">
                <div class="contact-card">
                    <div class="contact-icon">📍</div>
                    <h3>العنوان</h3>
                    <p>ليبيا، مصراتة، المقاوبة</p>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">📞</div>
                    <h3>الهاتف</h3>
                    <p>+218 945446851</p>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">✉️</div>
                    <h3>البريد الإلكتروني</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-card">
                    <div class="contact-icon">🕒</div>
                    <h3>ساعات العمل</h3>
                    <p>الأحد - الخميس: 8:00 ص - 6:00 م</p>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>© 2025 مجموعة ميثاق للأعمال والاستثمارات الهندسية - جميع الحقوق محفوظة</p>
                <p>تصميم وتطوير: فريق ميثاق التقني</p>
            </div>
        </div>
    </footer>

    <!-- زر الواتساب -->
    <div class="whatsapp-btn">
        <a href="https://wa.me/218945446851" target="_blank" title="تواصل عبر الواتساب">
            <span class="whatsapp-icon">💬</span>
            <span class="whatsapp-text">واتساب</span>
        </a>
    </div>
</body>
</html>'''

    def get_css(self):
        """ملف CSS"""
        return '''
/* MYTHAQ Standalone CSS */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Inter', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
}

.lang-en {
    font-family: 'Inter', Arial, sans-serif;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.gold {
    color: #CBA135;
}

.text-center {
    text-align: center;
}

/* Navigation */
.navbar {
    background: rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-brand h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.lang-btn {
    background: rgba(203,161,53,0.2);
    color: #CBA135;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    border: 1px solid #CBA135;
    transition: all 0.3s ease;
}

.lang-btn:hover {
    background: #CBA135;
    color: white;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-style: italic;
}

.hero-description {
    font-size: 1.1rem;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

/* Sections */
section {
    padding: 60px 0;
}

.about {
    background: rgba(255,255,255,0.05);
    backdrop-filter: blur(10px);
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.section-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.lead {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: rgba(255,255,255,0.9);
}

.about p {
    color: rgba(255,255,255,0.8);
    margin-bottom: 1rem;
}

.mission {
    background: rgba(203,161,53,0.1);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #CBA135;
    margin-top: 2rem;
}

.mission h3 {
    margin-bottom: 1rem;
}

/* Founder Card */
.founder-card {
    background: rgba(255,255,255,0.1);
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(203,161,53,0.3);
}

.founder-image {
    margin-bottom: 20px;
}

.founder-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #CBA135, #D4AF37);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 3px solid rgba(203,161,53,0.5);
}

.founder-icon {
    font-size: 3rem;
    color: white;
}

.founder-name {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.founder-title {
    color: rgba(255,255,255,0.8);
    margin-bottom: 1rem;
    font-style: italic;
}

.founder-desc {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

/* Services */
.services {
    background: rgba(0,0,0,0.1);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 3rem;
}

.service-card {
    background: rgba(255,255,255,0.1);
    padding: 30px 20px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(203,161,53,0.3);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.15);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-card h3 {
    color: #CBA135;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Contact */
.contact {
    background: rgba(255,255,255,0.05);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 3rem;
}

.contact-card {
    background: rgba(255,255,255,0.1);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(203,161,53,0.3);
}

.contact-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-card h3 {
    color: #CBA135;
    margin-bottom: 1rem;
}

.contact-card p {
    color: rgba(255,255,255,0.8);
}

/* Footer */
.footer {
    background: rgba(0,0,0,0.2);
    padding: 30px 0;
    text-align: center;
    color: rgba(255,255,255,0.7);
}

.footer-content p {
    margin-bottom: 0.5rem;
}

/* WhatsApp Button */
.whatsapp-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.whatsapp-btn a {
    display: flex;
    align-items: center;
    background: #25D366;
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(37,211,102,0.3);
    transition: all 0.3s ease;
}

.whatsapp-btn a:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37,211,102,0.4);
}

.whatsapp-icon {
    margin-left: 8px;
    font-size: 1.2rem;
}

.whatsapp-text {
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .services-grid,
    .contact-grid {
        grid-template-columns: 1fr;
    }

    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .hero {
        padding: 100px 0 60px;
    }
}

/* RTL Support */
[dir="rtl"] .whatsapp-btn {
    left: auto;
    right: 20px;
}

[dir="rtl"] .whatsapp-icon {
    margin-left: 0;
    margin-right: 8px;
}

[dir="rtl"] .mission {
    border-left: none;
    border-right: 4px solid #CBA135;
}
'''


def main():
    """تشغيل الخادم"""

    print("🚀 بدء تشغيل موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية")
    print("MYTHAQ Engineering Works & Investments Group")
    print("=" * 60)
    print(f"📍 الموقع متاح على: http://localhost:{PORT}")
    print(f"🌐 Website available at: http://localhost:{PORT}")
    print("⏹️  للإيقاف اضغط Ctrl+C / Press Ctrl+C to stop")
    print("-" * 60)

    try:
        # تغيير المجلد الحالي إلى مجلد المشروع
        os.chdir(os.path.dirname(os.path.abspath(__file__)))

        # إنشاء الخادم
        with socketserver.TCPServer(("", PORT), MythaqHandler) as httpd:
            print(f"✅ الخادم يعمل على المنفذ {PORT}")
            print(f"✅ Server running on port {PORT}")

            # فتح المتصفح تلقائياً
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 تم فتح المتصفح تلقائياً / Browser opened automatically")
            except:
                print("⚠️ لم يتم فتح المتصفح تلقائياً، افتحه يدوياً")
                print("⚠️ Browser not opened automatically, please open manually")

            print("\n🎉 الموقع جاهز! / Website is ready!")
            print("📱 يمكنك الآن زيارة الموقع / You can now visit the website")

            # تشغيل الخادم
            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n\n🔴 تم إيقاف الخادم / Server stopped")
        print("👋 شكراً لاستخدام موقع ميثاق / Thank you for using MYTHAQ website")

    except OSError as e:
        if e.errno == 10048:  # Address already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل / Port {PORT} is already in use")
            print("💡 جرب إغلاق البرامج الأخرى أو استخدم منفذ آخر")
            print("💡 Try closing other programs or use a different port")
        else:
            print(f"❌ خطأ في الشبكة / Network error: {e}")

    except Exception as e:
        print(f"❌ خطأ غير متوقع / Unexpected error: {e}")

    finally:
        input("\nاضغط Enter للخروج / Press Enter to exit...")


if __name__ == "__main__":
    main()