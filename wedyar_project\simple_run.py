#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from flask import Flask, render_template, session, request, redirect, url_for
    print("✅ Flask تم تحميله بنجاح")
except ImportError:
    print("❌ Flask غير مثبت. يرجى تثبيته باستخدام: pip install flask")
    sys.exit(1)

try:
    from flask_bootstrap import Bootstrap
    print("✅ Flask-Bootstrap تم تحميله بنجاح")
except ImportError:
    print("⚠️ Flask-Bootstrap غير مثبت. سيتم تشغيل الموقع بدونه")
    Bootstrap = None

# إنشاء التطبيق
app = Flask(__name__)
app.config["SECRET_KEY"] = "mythaq_secret_key_2025"
app.config['LANGUAGES'] = {
    'ar': 'العربية',
    'en': 'English'
}

# تهيئة Bootstrap إذا كان متاحاً
if Bootstrap:
    Bootstrap(app)

# الصفحة الرئيسية
@app.route('/')
def home():
    # تعيين اللغة الافتراضية
    if 'language' not in session:
        session['language'] = 'ar'
    
    try:
        return render_template('home_simple.html')
    except Exception as e:
        return f"""
        <html dir="rtl">
        <head>
            <title>موقع ميثاق</title>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .gold {{ color: #CBA135; }}
                .error {{ color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="gold">🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية</h1>
                <h2 class="gold">MYTHAQ Engineering Works & Investments</h2>
                <p>✅ الخادم يعمل بنجاح!</p>
                <div class="error">
                    <strong>خطأ في تحميل القالب:</strong><br>
                    {str(e)}
                </div>
                <p><strong>المؤسس:</strong> م. علي عمر باكير ابورقيبه</p>
                <p><strong>الموقع:</strong> ليبيا، مصراتة، المقاوبة</p>
                <p><strong>الهاتف:</strong> +218 945446851</p>
                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
            </div>
        </body>
        </html>
        """

# تغيير اللغة
@app.route('/set_language/<language>')
def set_language(language):
    session['language'] = language
    return redirect(url_for('home'))

if __name__ == "__main__":
    print("🚀 بدء تشغيل موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية...")
    print("📍 الموقع متاح على: http://127.0.0.1:5000")
    print("⏹️  للإيقاف اضغط Ctrl+C")
    print("-" * 60)
    
    try:
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=False  # تعطيل إعادة التحميل لتجنب المشاكل
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("💡 تأكد من تثبيت Flask: pip install flask")
        input("اضغط Enter للخروج...")
