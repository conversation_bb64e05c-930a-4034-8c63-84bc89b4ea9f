@echo off
chcp 65001 >nul
title موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية

echo.
echo ================================================================
echo   🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo   🚀 MYTHAQ Engineering Works & Investments Group
echo   📍 خادم ويب بسيط - لا يحتاج مكتبات خارجية
echo   📍 Simple Web Server - No External Libraries Required
echo ================================================================
echo.

cd /d "%~dp0"

echo 🔍 البحث عن Python...
echo 🔍 Looking for Python...

REM جرب python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo ✅ Python found
    echo.
    echo 🚀 تشغيل الخادم...
    echo 🚀 Starting server...
    python simple_web_server.py
    goto :end
)

REM جرب py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python ^(py^)
    echo ✅ Python found ^(py^)
    echo.
    echo 🚀 تشغيل الخادم...
    echo 🚀 Starting server...
    py simple_web_server.py
    goto :end
)

REM جرب python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python3
    echo ✅ Python3 found
    echo.
    echo 🚀 تشغيل الخادم...
    echo 🚀 Starting server...
    python3 simple_web_server.py
    goto :end
)

REM إذا لم يتم العثور على Python
echo ❌ لم يتم العثور على Python
echo ❌ Python not found
echo.
echo 💡 يرجى تثبيت Python من:
echo 💡 Please install Python from:
echo    https://python.org
echo.
echo 📋 تعليمات التثبيت:
echo 📋 Installation instructions:
echo    1. قم بتحميل Python من الرابط أعلاه
echo    1. Download Python from the link above
echo    2. أثناء التثبيت، تأكد من تحديد "Add Python to PATH"
echo    2. During installation, make sure to check "Add Python to PATH"
echo    3. أعد تشغيل هذا الملف بعد التثبيت
echo    3. Run this file again after installation
echo.
echo 🌐 بديل: يمكنك فتح ملف mythaq_website.html مباشرة في المتصفح
echo 🌐 Alternative: You can open mythaq_website.html directly in browser
echo.

:end
echo.
echo 🔴 تم إنهاء البرنامج
echo 🔴 Program ended
echo.
pause
