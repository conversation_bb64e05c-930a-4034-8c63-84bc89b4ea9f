from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html>
    <head>
        <title>MYTHAQ Test</title>
        <style>
            body { font-family: Arial; margin: 40px; background: #1a1a1a; color: white; }
            .container { max-width: 800px; margin: 0 auto; text-align: center; }
            .logo { color: #CBA135; font-size: 3em; margin-bottom: 20px; }
            .success { color: #28a745; font-size: 1.5em; margin: 20px 0; }
            .info { background: #2d2d2d; padding: 20px; border-radius: 10px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="logo">🏗️ MYTHAQ</h1>
            <h2 class="logo">ميتاق للاستشارات الهندسية</h2>
            
            <div class="success">✅ الخادم يعمل بنجاح!</div>
            
            <div class="info">
                <h3>🎉 تم تشغيل البرنامج بنجاح</h3>
                <p>إذا كنت ترى هذه الصفحة، فهذا يعني أن:</p>
                <ul style="text-align: left;">
                    <li>✅ Python يعمل بشكل صحيح</li>
                    <li>✅ Flask مثبت ويعمل</li>
                    <li>✅ الخادم بدأ بنجاح</li>
                    <li>✅ المتصفح يتصل بالخادم</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>📞 معلومات الاتصال</h3>
                <p><strong>الهاتف:</strong> +218 945446851</p>
                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p><strong>الموقع:</strong> ليبيا، مصراتة، المقاوبة</p>
            </div>
            
            <div class="info">
                <h3>🔧 الخطوات التالية</h3>
                <p>الآن يمكنك:</p>
                <ol style="text-align: left;">
                    <li>إيقاف هذا الخادم (Ctrl+C)</li>
                    <li>تشغيل الموقع الكامل: <code>python run.py</code></li>
                    <li>أو استخدام: <code>quick_start.bat</code></li>
                </ol>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🧪 تشغيل خادم الاختبار البسيط...")
    print("📍 الموقع: http://127.0.0.1:5000")
    print("⏹️  للإيقاف: Ctrl+C")
    print("-" * 40)
    
    try:
        app.run(debug=True, host='127.0.0.1', port=5000)
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف الخادم بنجاح")
    except Exception as e:
        print(f"❌ خطأ: {e}")
