from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>MYTHAQ Test</title>
        <meta charset="UTF-8">
    </head>
    <body>
        <h1>🚀 MYTHAQ Engineering & Consultancy</h1>
        <h2>✅ الخادم يعمل بشكل صحيح!</h2>
        <p>إذا كنت ترى هذه الرسالة، فهذا يعني أن الخادم يعمل بنجاح.</p>
        <hr>
        <h3>المشكلة المحتملة:</h3>
        <ul>
            <li>الملف الأصلي كبير جداً (53KB)</li>
            <li>قد يكون هناك خطأ في HTML أو CSS</li>
            <li>مشكلة في استيراد الملفات</li>
        </ul>
        <hr>
        <h3>الحلول:</h3>
        <ol>
            <li>تقسيم الملف إلى أجزاء أصغر</li>
            <li>فحص الأخطاء في الكود</li>
            <li>استخدام ملفات منفصلة للأقسام</li>
        </ol>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🧪 تشغيل خادم الاختبار...")
    print("📍 http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
