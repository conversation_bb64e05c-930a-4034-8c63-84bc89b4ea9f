@echo off
chcp 65001 >nul
title WEDYAR Engineering Consultancy Server
color 0A

echo ============================================================
echo                WEDYAR Engineering Consultancy
echo ============================================================
echo.

echo [INFO] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

echo [INFO] Python found successfully
echo.

echo [INFO] Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] Failed to install requirements
    echo.
    pause
    exit /b 1
)

echo.
echo [INFO] Starting WEDYAR server...
echo [INFO] Server will be available at: http://127.0.0.1:5000
echo [INFO] Press Ctrl+C to stop the server
echo.

python run.py

echo.
echo [INFO] Server stopped
pause
