# WEDYAR Engineering Consultancy Server Startup Script
# PowerShell Version

Write-Host "============================================================" -ForegroundColor Green
Write-Host "                WEDYAR Engineering Consultancy" -ForegroundColor Yellow
Write-Host "============================================================" -ForegroundColor Green
Write-Host ""

# Check Python installation
Write-Host "[INFO] Checking Python installation..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[INFO] Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python from https://python.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Install requirements
Write-Host "[INFO] Installing requirements..." -ForegroundColor Cyan
try {
    pip install -r requirements.txt
    Write-Host "[INFO] Requirements installed successfully" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to install requirements" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[INFO] Starting WEDYAR server..." -ForegroundColor Cyan
Write-Host "[INFO] Server will be available at: http://127.0.0.1:5000" -ForegroundColor Yellow
Write-Host "[INFO] Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the server
try {
    python run.py
} catch {
    Write-Host "[ERROR] Failed to start server" -ForegroundColor Red
}

Write-Host ""
Write-Host "[INFO] Server stopped" -ForegroundColor Yellow
Read-Host "Press Enter to exit"
