# دليل حل المشاكل - موقع ميثاق
## Troubleshooting Guide - MYTHAQ Website

### 📅 التاريخ / Date: 2025-07-13
### 🎯 المشكلة / Issue: عدم تشغيل البرنامج

---

## 🔍 تشخيص المشكلة / Problem Diagnosis

### ❌ **المشكلة الحالية / Current Issue**
- البرنامج لا يعمل عند التشغيل
- جميع محاولات التشغيل تفشل مع return code -1
- لا توجد رسائل خطأ واضحة

### 🔍 **الأسباب المحتملة / Possible Causes**
1. **Python غير مثبت** أو غير متاح في PATH
2. **Flask غير مثبت**
3. **مشكلة في مسار الملفات**
4. **مشكلة في أذونات التشغيل**
5. **تعارض في إصدارات Python**

---

## 🛠️ الحلول المقترحة / Suggested Solutions

### 1️⃣ **التحقق من Python**
```cmd
# افتح Command Prompt وجرب:
python --version
# أو
python3 --version
# أو
py --version
```

**إذا لم يعمل أي منها:**
- قم بتحميل Python من: https://python.org
- تأكد من تحديد "Add Python to PATH" أثناء التثبيت

### 2️⃣ **تثبيت Flask**
```cmd
# بعد التأكد من وجود Python:
pip install flask
# أو
pip install flask flask-bootstrap4
```

### 3️⃣ **التشغيل اليدوي**
```cmd
# انتقل إلى مجلد المشروع:
cd C:\Users\<USER>\Desktop\wedyar_project

# جرب تشغيل الملف المباشر:
python direct_run.py

# أو الملف المبسط:
python simple_run.py

# أو الملف الأصلي:
python run.py
```

### 4️⃣ **استخدام ملف Batch**
- انقر نقراً مزدوجاً على `START_MYTHAQ.bat`
- سيقوم بفحص وتثبيت المتطلبات تلقائياً

---

## 📁 ملفات التشغيل المتاحة / Available Run Files

### ✅ **الملفات الجاهزة للتشغيل:**

1. **`direct_run.py`** - ملف تشغيل مباشر مبسط
   - لا يحتاج ملفات HTML منفصلة
   - يعرض الموقع مباشرة في الكود

2. **`simple_run.py`** - ملف تشغيل مع معالجة أخطاء
   - يحاول تحميل القوالب
   - يعرض صفحة احتياطية عند الفشل

3. **`run.py`** - الملف الأصلي
   - يحتاج جميع الملفات والمكتبات

4. **`START_MYTHAQ.bat`** - ملف تشغيل تلقائي
   - يفحص ويثبت المتطلبات
   - يشغل الموقع تلقائياً

---

## 🎯 خطوات التشغيل الموصى بها / Recommended Steps

### 📋 **الطريقة الأولى - التشغيل المباشر:**
1. افتح Command Prompt كمدير (Run as Administrator)
2. انتقل للمجلد: `cd C:\Users\<USER>\Desktop\wedyar_project`
3. شغل الأمر: `python direct_run.py`
4. افتح المتصفح على: `http://127.0.0.1:5000`

### 📋 **الطريقة الثانية - ملف Batch:**
1. انقر نقراً مزدوجاً على `START_MYTHAQ.bat`
2. اتبع التعليمات على الشاشة
3. افتح المتصفح على: `http://127.0.0.1:5000`

### 📋 **الطريقة الثالثة - PowerShell:**
1. افتح PowerShell كمدير
2. شغل الأمر: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
3. انتقل للمجلد: `cd "C:\Users\<USER>\Desktop\wedyar_project"`
4. شغل الأمر: `python direct_run.py`

---

## 🔧 إعدادات بديلة / Alternative Settings

### 🌐 **إذا لم تعمل الطرق السابقة:**

#### **استخدام Python من Microsoft Store:**
```cmd
# ثبت Python من Microsoft Store
# ثم جرب:
python.exe direct_run.py
```

#### **استخدام Virtual Environment:**
```cmd
# إنشاء بيئة افتراضية:
python -m venv mythaq_env

# تفعيلها:
mythaq_env\Scripts\activate

# تثبيت المتطلبات:
pip install flask

# تشغيل الموقع:
python direct_run.py
```

---

## 📞 معلومات الدعم / Support Information

### 🆘 **إذا استمرت المشكلة:**

1. **تحقق من إصدار Python:**
   - يجب أن يكون Python 3.7 أو أحدث
   - استخدم: `python --version`

2. **تحقق من pip:**
   - استخدم: `pip --version`
   - إذا لم يعمل، جرب: `python -m pip --version`

3. **تثبيت المتطلبات يدوياً:**
   ```cmd
   pip install flask
   pip install flask-bootstrap4
   pip install werkzeug
   ```

4. **تشغيل بوضع verbose:**
   ```cmd
   python -v direct_run.py
   ```

---

## 🎉 النتيجة المتوقعة / Expected Result

### ✅ **عند نجاح التشغيل ستحصل على:**
- رسالة: "🚀 بدء تشغيل موقع مجموعة ميثاق..."
- رسالة: "📍 الموقع متاح على: http://127.0.0.1:5000"
- موقع يعمل على المتصفح يعرض:
  - اسم الشركة: مجموعة ميثاق للأعمال والاستثمارات الهندسية
  - معلومات المؤسس: م. علي عمر باكير ابورقيبه
  - معلومات الاتصال والخدمات

---

## 📋 قائمة التحقق السريعة / Quick Checklist

- [ ] Python مثبت ويعمل
- [ ] pip متاح ويعمل
- [ ] Flask مثبت
- [ ] المجلد الصحيح مفتوح
- [ ] الملف موجود ويمكن الوصول إليه
- [ ] المنفذ 5000 غير مستخدم
- [ ] لا توجد برامج حماية تمنع التشغيل

---

**💡 نصيحة: ابدأ بـ `direct_run.py` لأنه الأبسط والأكثر استقلالية!**

---

*تم إنشاء هذا الدليل بواسطة Augment Agent*
*Troubleshooting guide created by Augment Agent*
