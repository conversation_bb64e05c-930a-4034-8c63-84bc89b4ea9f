@echo off
chcp 65001 >nul
title موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية

echo.
echo ================================================================
echo   🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo   🚀 MYTHAQ Engineering Works & Investments Group
echo ================================================================
echo.

REM الانتقال إلى مجلد المشروع
cd /d "%~dp0"

echo 📍 المجلد الحالي: %CD%
echo 📍 Current directory: %CD%
echo.

echo 🔍 البحث عن Python...
echo 🔍 Looking for Python...

REM جرب python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo ✅ Python found
    python --version
    echo.
    echo 🚀 تشغيل الخادم على المنفذ 8000...
    echo 🚀 Starting server on port 8000...
    echo 📍 الموقع سيكون متاحاً على: http://localhost:8000
    echo 📍 Website will be available at: http://localhost:8000
    echo.
    python simple_web_server.py
    goto :end
)

REM جرب py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python ^(py^)
    echo ✅ Python found ^(py^)
    py --version
    echo.
    echo 🚀 تشغيل الخادم على المنفذ 8000...
    echo 🚀 Starting server on port 8000...
    echo 📍 الموقع سيكون متاحاً على: http://localhost:8000
    echo 📍 Website will be available at: http://localhost:8000
    echo.
    py simple_web_server.py
    goto :end
)

REM جرب python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python3
    echo ✅ Python3 found
    python3 --version
    echo.
    echo 🚀 تشغيل الخادم على المنفذ 8000...
    echo 🚀 Starting server on port 8000...
    echo 📍 الموقع سيكون متاحاً على: http://localhost:8000
    echo 📍 Website will be available at: http://localhost:8000
    echo.
    python3 simple_web_server.py
    goto :end
)

REM إذا لم يتم العثور على Python
echo ❌ لم يتم العثور على Python
echo ❌ Python not found
echo.
echo 💡 يرجى تثبيت Python من:
echo 💡 Please install Python from:
echo    https://python.org
echo.
echo 🌐 بديل: فتح الموقع مباشرة من ملف HTML
echo 🌐 Alternative: Open website directly from HTML file
echo.
echo 📂 يمكنك فتح الملف التالي في المتصفح:
echo 📂 You can open the following file in browser:
echo    %CD%\mythaq_website.html
echo.

REM فتح ملف HTML مباشرة
echo 🚀 فتح الموقع في المتصفح...
echo 🚀 Opening website in browser...
start "" "%CD%\mythaq_website.html"

:end
echo.
echo 🔴 تم إنهاء البرنامج
echo 🔴 Program ended
echo.
pause
