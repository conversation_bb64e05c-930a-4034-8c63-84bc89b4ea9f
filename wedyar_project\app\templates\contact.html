{% extends "base.html" %}

{% block title %}
  {% set current_lang = session.get('language', 'ar') %}
  {% if current_lang == 'en' %}Contact Us - MYTHAQ Engineering & Consultancy{% else %}اتصل بنا - ميتاق للاستشارات الهندسية{% endif %}
{% endblock %}

{% block content %}
<div class="container py-5">
  {% set current_lang = session.get('language', 'ar') %}
  <!-- Header Section -->
  <div class="row text-center mb-5" data-aos="fade-up">
    <div class="col-12">
      {% if current_lang == 'en' %}
      <h1 class="display-4 fw-bold gold-text mb-4">Contact Us</h1>
      <p class="lead text-muted">We are here to help you achieve your architectural project. Contact us today for a free consultation</p>
      {% else %}
      <h1 class="display-4 fw-bold gold-text mb-4">تواصل معنا</h1>
      <p class="lead text-muted">نحن هنا لمساعدتك في تحقيق مشروعك المعماري. تواصل معنا اليوم للحصول على استشارة مجانية</p>
      {% endif %}
    </div>
  </div>

  <div class="row g-5">
    <!-- Contact Form -->
    <div class="col-lg-8" data-aos="fade-right">
      <div class="card shadow-lg border-0">
        <div class="card-body p-5">
          {% if current_lang == 'en' %}
          <h3 class="gold-text mb-4">
            <i class="fas fa-envelope me-2"></i>Send us a message
          </h3>
          <form method="POST" action="{{ url_for('main.contact') }}">
            <div class="row g-3">
              <div class="col-md-6">
                <label for="name" class="form-label">
                  <i class="fas fa-user me-2 text-warning"></i>Full Name
                </label>
                <input type="text" class="form-control" id="name" name="name" placeholder="Enter your full name" required />
              </div>
              <div class="col-md-6">
                <label for="email" class="form-label">
                  <i class="fas fa-envelope me-2 text-warning"></i>Email Address
                </label>
                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required
                       pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                       title="Please enter a valid email address using English letters only (e.g., <EMAIL>)"
                       style="direction: ltr; text-align: left;"
                       oninput="this.value = this.value.replace(/[^a-zA-Z0-9._%+-@]/g, '')" />
              </div>
              <div class="col-md-6">
                <label for="phone" class="form-label">
                  <i class="fas fa-phone me-2 text-warning"></i>Phone Number
                </label>
                <input type="tel" class="form-control" id="phone" name="phone" placeholder="+218 945446851"
                       pattern="[0-9+\s\-()]+"
                       title="Please enter numbers only (0-9, +, -, space, parentheses allowed)"
                       style="direction: ltr; text-align: left;"
                       oninput="this.value = this.value.replace(/[^0-9+\s\-()]/g, '')" />
              </div>
              <div class="col-md-6">
                <label for="subject" class="form-label">
                  <i class="fas fa-tag me-2 text-warning"></i>Message Subject
                </label>
                <select class="form-control" id="subject" name="subject" required>
                  <option value="">Choose message subject</option>
                  <option value="consultation">Engineering Consultation</option>
                  <option value="design">Design Request</option>
                  <option value="supervision">Project Supervision</option>
                  <option value="renovation">Renovation & Maintenance</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div class="col-12">
                <label for="message" class="form-label">
                  <i class="fas fa-comment me-2 text-warning"></i>Project Details or Inquiry
                </label>
                <textarea class="form-control" id="message" name="message" rows="6"
                          placeholder="Write your project details or inquiry here..." required></textarea>
              </div>
              <div class="col-12">
                <button type="submit" class="btn btn-gold btn-lg w-100">
                  <i class="fas fa-paper-plane me-2"></i>Send Message
                </button>
              </div>
            </div>
          </form>
          {% else %}
          <h3 class="gold-text mb-4">
            <i class="fas fa-envelope me-2"></i>أرسل لنا رسالة
          </h3>
          <form method="POST" action="{{ url_for('main.contact') }}">
            <div class="row g-3">
              <div class="col-md-6">
                <label for="name" class="form-label">
                  <i class="fas fa-user me-2 text-warning"></i>الاسم الكامل
                </label>
                <input type="text" class="form-control" id="name" name="name" placeholder="أدخل اسمك الكامل" required />
              </div>
              <div class="col-md-6">
                <label for="email" class="form-label">
                  <i class="fas fa-envelope me-2 text-warning"></i>البريد الإلكتروني
                </label>
                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required
                       pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                       title="يرجى إدخال عنوان بريد إلكتروني صحيح باللغة الإنجليزية فقط (مثل: <EMAIL>)"
                       style="direction: ltr; text-align: left;"
                       oninput="this.value = this.value.replace(/[^a-zA-Z0-9._%+-@]/g, '')" />
              </div>
              <div class="col-md-6">
                <label for="phone" class="form-label">
                  <i class="fas fa-phone me-2 text-warning"></i>رقم الهاتف
                </label>
                <input type="tel" class="form-control" id="phone" name="phone" placeholder="218+ 945446851"
                       pattern="[0-9+\s\-()]+"
                       title="يرجى إدخال الأرقام فقط (0-9، +، -، مسافة، أقواس مسموحة)"
                       style="direction: ltr; text-align: left;"
                       oninput="this.value = this.value.replace(/[^0-9+\s\-()]/g, '')" />
              </div>
              <div class="col-md-6">
                <label for="subject" class="form-label">
                  <i class="fas fa-tag me-2 text-warning"></i>موضوع الرسالة
                </label>
                <select class="form-control" id="subject" name="subject" required>
                  <option value="">اختر موضوع الرسالة</option>
                  <option value="consultation">استشارة هندسية</option>
                  <option value="design">طلب تصميم</option>
                  <option value="supervision">إشراف على مشروع</option>
                  <option value="renovation">ترميم وصيانة</option>
                  <option value="other">أخرى</option>
                </select>
              </div>
              <div class="col-12">
                <label for="message" class="form-label">
                  <i class="fas fa-comment me-2 text-warning"></i>تفاصيل المشروع أو الاستفسار
                </label>
                <textarea class="form-control" id="message" name="message" rows="6"
                          placeholder="اكتب تفاصيل مشروعك أو استفسارك هنا..." required></textarea>
              </div>
              <div class="col-12">
                <button type="submit" class="btn btn-gold btn-lg w-100">
                  <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                </button>
              </div>
            </div>
          </form>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Contact Info -->
    <div class="col-lg-4" data-aos="fade-left">
      <!-- Contact Cards -->
      <div class="row g-3">
        <!-- Phone -->
        <div class="col-12">
          <div class="card h-100 text-center border-0 shadow">
            <div class="card-body p-4">
              <div class="feature-icon mb-3">
                <i class="fas fa-phone fa-2x"></i>
              </div>
              {% if current_lang == 'en' %}
              <h5 class="card-title">Call Us</h5>
              <p class="card-text">
                <strong>945446851 218+</strong><br>
                <small class="text-muted">Available 24/7</small>
              </p>
              <a href="tel:+218945446851" class="btn btn-outline-gold btn-sm">
                <i class="fas fa-phone me-1"></i>Call Now
              </a>
              {% else %}
              <h5 class="card-title">اتصل بنا</h5>
              <p class="card-text">
                <strong>945446851 218+</strong><br>
                <small class="text-muted">متاح 24/7</small>
              </p>
              <a href="tel:+218945446851" class="btn btn-outline-gold btn-sm">
                <i class="fas fa-phone me-1"></i>اتصل الآن
              </a>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- WhatsApp -->
        <div class="col-12">
          <div class="card h-100 text-center border-0 shadow">
            <div class="card-body p-4">
              <div class="feature-icon mb-3">
                <i class="fab fa-whatsapp fa-2x"></i>
              </div>
              {% if current_lang == 'en' %}
              <h5 class="card-title">WhatsApp</h5>
              <p class="card-text">
                <strong>945446851 218+</strong><br>
                <small class="text-muted">Quick Response</small>
              </p>
              <a href="https://wa.me/218945446851" target="_blank" class="btn btn-outline-gold btn-sm">
                <i class="fab fa-whatsapp me-1"></i>Message Us
              </a>
              {% else %}
              <h5 class="card-title">واتساب</h5>
              <p class="card-text">
                <strong>945446851 218+</strong><br>
                <small class="text-muted">رد سريع</small>
              </p>
              <a href="https://wa.me/218945446851" target="_blank" class="btn btn-outline-gold btn-sm">
                <i class="fab fa-whatsapp me-1"></i>راسلنا
              </a>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Email -->
        <div class="col-12">
          <div class="card h-100 text-center border-0 shadow">
            <div class="card-body p-4">
              <div class="feature-icon mb-3">
                <i class="fas fa-envelope fa-2x"></i>
              </div>
              {% if current_lang == 'en' %}
              <h5 class="card-title">Email</h5>
              <p class="card-text">
                <strong><EMAIL></strong><br>
                <small class="text-muted">Reply within 24 hours</small>
              </p>
              <a href="mailto:<EMAIL>" class="btn btn-outline-gold btn-sm">
                <i class="fas fa-envelope me-1"></i>Send Email
              </a>
              {% else %}
              <h5 class="card-title">البريد الإلكتروني</h5>
              <p class="card-text">
                <strong><EMAIL></strong><br>
                <small class="text-muted">نرد خلال 24 ساعة</small>
              </p>
              <a href="mailto:<EMAIL>" class="btn btn-outline-gold btn-sm">
                <i class="fas fa-envelope me-1"></i>أرسل إيميل
              </a>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Location -->
        <div class="col-12">
          <div class="card h-100 text-center border-0 shadow">
            <div class="card-body p-4">
              <div class="feature-icon mb-3">
                <i class="fas fa-map-marker-alt fa-2x"></i>
              </div>
              {% if current_lang == 'en' %}
              <h5 class="card-title">Office Location</h5>
              <p class="card-text">
                <strong>Misrata Al-Maqawba, Libya</strong><br>
                <small class="text-muted">Al-Maqawba District</small>
              </p>
              <a href="#map" class="btn btn-outline-gold btn-sm">
                <i class="fas fa-map me-1"></i>View Map
              </a>
              {% else %}
              <h5 class="card-title">موقع المكتب</h5>
              <p class="card-text">
                <strong>مصراتة المقاوبة، ليبيا</strong><br>
                <small class="text-muted">منطقة المقاوبة</small>
              </p>
              <a href="#map" class="btn btn-outline-gold btn-sm">
                <i class="fas fa-map me-1"></i>عرض الخريطة
              </a>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Map Section -->
  <div class="row mt-5" data-aos="fade-up">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-transparent border-0 p-4">
          <h3 class="gold-text mb-0">
            {% if current_lang == 'en' %}
            <i class="fas fa-map-marker-alt me-2"></i>Our Location on Map
            {% else %}
            <i class="fas fa-map-marker-alt me-2"></i>موقعنا على الخريطة
            {% endif %}
          </h3>
        </div>
        <div class="card-body p-0">
          <iframe id="map"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.086538412351!2d15.087267!3d32.374166!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x13a5e2c6f1234567%3A0x1234567890abcdef!2sMisrata%2C+Libya!5e0!3m2!1sar!2sly!4v1685528391234!5m2!1sar!2sly"
            width="100%" height="400" style="border:0; border-radius: 0 0 24px 24px;" allowfullscreen="" loading="lazy">
          </iframe>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين التحقق من صحة البيانات
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    const emailInputs = document.querySelectorAll('input[type="email"]');

    // تحسين حقل الهاتف
    phoneInputs.forEach(function(phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // السماح فقط بالأرقام والرموز المحددة
            let value = e.target.value;
            let cleanValue = value.replace(/[^0-9+\s\-()]/g, '');

            if (value !== cleanValue) {
                e.target.value = cleanValue;
                // إظهار رسالة تنبيه
                showValidationMessage(e.target, 'يُسمح بالأرقام والرموز (+، -، مسافة، أقواس) فقط', 'error');
            }
        });

        phoneInput.addEventListener('blur', function(e) {
            const value = e.target.value.trim();
            if (value && !/^[0-9+\s\-()]+$/.test(value)) {
                showValidationMessage(e.target, 'رقم الهاتف غير صحيح', 'error');
            } else if (value) {
                showValidationMessage(e.target, 'رقم الهاتف صحيح', 'success');
            }
        });
    });

    // تحسين حقل الإيميل
    emailInputs.forEach(function(emailInput) {
        emailInput.addEventListener('input', function(e) {
            // السماح فقط بالأحرف الإنجليزية والأرقام والرموز المسموحة
            let value = e.target.value;
            let cleanValue = value.replace(/[^a-zA-Z0-9._%+-@]/g, '');

            if (value !== cleanValue) {
                e.target.value = cleanValue;
                showValidationMessage(e.target, 'يُسمح بالأحرف الإنجليزية والأرقام والرموز المسموحة فقط', 'error');
            }
        });

        emailInput.addEventListener('blur', function(e) {
            const value = e.target.value.trim();
            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (value && !emailPattern.test(value)) {
                showValidationMessage(e.target, 'عنوان البريد الإلكتروني غير صحيح أو يحتوي على أحرف غير إنجليزية', 'error');
            } else if (value) {
                showValidationMessage(e.target, 'عنوان البريد الإلكتروني صحيح', 'success');
            }
        });
    });

    // دالة إظهار رسائل التحقق
    function showValidationMessage(input, message, type) {
        // إزالة الرسائل السابقة
        const existingMessage = input.parentNode.querySelector('.validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // إنشاء رسالة جديدة
        const messageDiv = document.createElement('div');
        messageDiv.className = `validation-message small mt-1 ${type === 'error' ? 'text-danger' : 'text-success'}`;
        messageDiv.innerHTML = `<i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'} me-1"></i>${message}`;

        // إضافة الرسالة
        input.parentNode.appendChild(messageDiv);

        // إزالة الرسالة بعد 3 ثوانٍ
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }

    // تحسين إرسال النموذج
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const phoneInput = form.querySelector('input[type="tel"]');
            const emailInput = form.querySelector('input[type="email"]');

            let isValid = true;

            // التحقق من الهاتف
            if (phoneInput && phoneInput.value.trim()) {
                const phoneValue = phoneInput.value.trim();
                if (!/^[0-9+\s\-()]+$/.test(phoneValue)) {
                    showValidationMessage(phoneInput, 'رقم الهاتف يحتوي على أحرف غير مسموحة', 'error');
                    isValid = false;
                }
            }

            // التحقق من الإيميل
            if (emailInput && emailInput.value.trim()) {
                const emailValue = emailInput.value.trim();
                const emailPattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
                if (!emailPattern.test(emailValue)) {
                    showValidationMessage(emailInput, 'عنوان البريد الإلكتروني غير صحيح', 'error');
                    isValid = false;
                }
            }

            if (!isValid) {
                e.preventDefault();
                // التمرير إلى أول خطأ
                const firstError = form.querySelector('.validation-message.text-danger');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    });
});
</script>
{% endblock %}
