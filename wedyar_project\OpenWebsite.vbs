Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' الحصول على مجلد السكريبت
strScriptPath = objFSO.GetParentFolderName(WScript.ScriptFullName)

' تحديد ملف HTML
strHtmlFile = ""
If objFSO.FileExists(strScriptPath & "\mythaq_final.html") Then
    strHtmlFile = strScriptPath & "\mythaq_final.html"
ElseIf objFSO.FileExists(strScriptPath & "\mythaq_website.html") Then
    strHtmlFile = strScriptPath & "\mythaq_website.html"
End If

' فتح الملف
If strHtmlFile <> "" Then
    objShell.Run """" & strHtmlFile & """", 1, False
    MsgBox "تم فتح موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية" & vbCrLf & "MYTHAQ Engineering Works & Investments website opened", vbInformation, "موقع ميثاق - MYTHAQ Website"
Else
    MsgBox "لم يتم العثور على ملف الموقع" & vbCrLf & "Website file not found", vbCritical, "خطأ - Error"
End If
