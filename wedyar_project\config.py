# إعدادات البريد الإلكتروني لموقع METAQ
import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your_secret_key_here'
    
    # إعدادات Gmail SMTP
    MAIL_SERVER = 'smtp.gmail.com'
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') or '<EMAIL>'
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') or ''  # App Password
    MAIL_DEFAULT_SENDER = '<EMAIL>'
    
    # إعدادات اللغات
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }

# ملاحظات مهمة:
# 1. يجب إنشاء App Password من إعدادات Gmail
# 2. تفعيل Two-Factor Authentication
# 3. استخدام App Password بدلاً من كلمة المرور العادية
