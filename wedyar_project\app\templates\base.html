<!DOCTYPE html>
<html lang="{% if session.language == 'en' %}en{% else %}ar{% endif %}"
      dir="{% if session.language == 'en' %}ltr{% else %}rtl{% endif %}"
      class="{% if session.language == 'en' %}lang-en{% else %}lang-ar{% endif %}">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{% block title %}MYTHAQ Engineering & Consultancy - شركة ميتاق للاستشارات الهندسية{% endblock %}</title>
  <meta name="description" content="شركة ميتاق للاستشارات والأعمال الهندسية - نقدم أفضل الحلول المعمارية والهندسية في ليبيا" />
  <meta name="keywords" content="هندسة, معمارية, استشارات, ليبيا, تصميم, إنشاءات, ميتاق, MYTHAQ" />

  <!-- Bootstrap & Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet" />

  <!-- Custom CSS -->
  <link href="{{ url_for('static', filename='css/style.css') }}?v=2.0" rel="stylesheet" />

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}" />
</head>
<body>
  <!-- Enhanced Navbar -->
  <nav class="navbar navbar-expand-lg fixed-top" id="mainNavbar">
    <div class="container">
      <a class="navbar-brand" href="{{ url_for('main.home') }}">
        <i class="fas fa-building me-2"></i>{% if session.language == 'en' %}MYTHAQ{% else %}ميتاق{% endif %}
      </a>

      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navmenu">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navmenu">
        <ul class="navbar-nav ms-auto">
          {% set current_lang = session.get('language', 'ar') %}
          {% if current_lang == 'en' %}
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.home') }}">
              <i class="fas fa-home me-1"></i>Home
            </a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="{{ url_for('main.services') }}" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-cogs me-1"></i>Services
            </a>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#architectural">Architectural Design</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#supervision">Supervision & Implementation</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#renovation">Renovation & Maintenance</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#consultation">Engineering Consultation</a></li>
            </ul>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.projects') }}">
              <i class="fas fa-project-diagram me-1"></i>Projects
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.team') }}">
              <i class="fas fa-users me-1"></i>Our Team
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.about') }}">
              <i class="fas fa-info-circle me-1"></i>About Us
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.contact') }}">
              <i class="fas fa-envelope me-1"></i>Contact Us
            </a>
          </li>
          {% else %}
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.home') }}">
              <i class="fas fa-home me-1"></i>الرئيسية
            </a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="{{ url_for('main.services') }}" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-cogs me-1"></i>خدماتنا
            </a>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#architectural">التصميم المعماري</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#supervision">الإشراف والتنفيذ</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#renovation">الترميم والصيانة</a></li>
              <li><a class="dropdown-item" href="{{ url_for('main.services') }}#consultation">الاستشارات الهندسية</a></li>
            </ul>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.projects') }}">
              <i class="fas fa-project-diagram me-1"></i>مشاريعنا
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.team') }}">
              <i class="fas fa-users me-1"></i>فريق العمل
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.about') }}">
              <i class="fas fa-info-circle me-1"></i>من نحن
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('main.contact') }}">
              <i class="fas fa-envelope me-1"></i>اتصل بنا
            </a>
          </li>
          {% endif %}
        </ul>

        <!-- Language Switcher -->
        <div class="d-flex align-items-center ms-3">
          <div class="dropdown">
            <button class="btn dropdown-toggle language-btn" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-globe me-2"></i>
              {% set current_lang = session.get('language', 'ar') %}
              {% if current_lang == 'en' %}
                <span class="fw-bold">English</span>
              {% else %}
                <span class="fw-bold">العربية</span>
              {% endif %}
            </button>
            <ul class="dropdown-menu dropdown-menu-end language-dropdown" aria-labelledby="languageDropdown">
              <li>
                <a class="dropdown-item {% if current_lang == 'ar' %}active{% endif %}"
                   href="{{ url_for('main.set_language', language='ar') }}">
                  <i class="fas fa-flag me-2"></i>العربية
                </a>
              </li>
              <li>
                <a class="dropdown-item {% if current_lang == 'en' %}active{% endif %}"
                   href="{{ url_for('main.set_language', language='en') }}">
                  <i class="fas fa-flag me-2"></i>English
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>



  <!-- Main Content -->
  <main style="padding-top: 80px;">
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div class="container mt-3">
          {% for category, message in messages %}
            <div class="alert alert-{{ 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
              <i class="fas fa-{{ 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}

    {% block content %}
    {% endblock %}
  </main>

  <!-- Enhanced Footer -->
  <footer class="text-white">
    <div class="container py-5">
      {% set current_lang = session.get('language', 'ar') %}
      <div class="row">
        <div class="col-lg-4 mb-4">
          <h5 class="gold-text mb-3">
            <i class="fas fa-building me-2"></i>{% if current_lang == 'en' %}MYTHAQ{% else %}ميتاق{% endif %}
          </h5>
          {% if current_lang == 'en' %}
          <p class="mb-3">MYTHAQ Engineering Consultancy - We transform your dreams into stunning architectural reality</p>
          <div class="social-icons">
            <a href="#" title="Facebook"><i class="fab fa-facebook-f"></i></a>
            <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
            <a href="#" title="X (Twitter)"><i class="fab fa-x-twitter"></i></a>
            <a href="https://wa.me/218945446851" title="WhatsApp"><i class="fab fa-whatsapp"></i></a>
          </div>
          {% else %}
          <p class="mb-3">شركة ميتاق للاستشارات والأعمال الهندسية - نحول أحلامكم إلى واقع معماري مبهر</p>
          <div class="social-icons">
            <a href="#" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
            <a href="#" title="إنستغرام"><i class="fab fa-instagram"></i></a>
            <a href="#" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
            <a href="#" title="إكس (تويتر)"><i class="fab fa-x-twitter"></i></a>
            <a href="https://wa.me/218945446851" title="واتساب"><i class="fab fa-whatsapp"></i></a>
          </div>
          {% endif %}
        </div>

        <div class="col-lg-2 col-md-6 mb-4">
          {% if current_lang == 'en' %}
          <h6 class="gold-text mb-3">Quick Links</h6>
          <ul class="list-unstyled">
            <li><a href="{{ url_for('main.home') }}" class="text-white-50 text-decoration-none">Home</a></li>
            <li><a href="{{ url_for('main.about') }}" class="text-white-50 text-decoration-none">About Us</a></li>
            <li><a href="{{ url_for('main.services') }}" class="text-white-50 text-decoration-none">Services</a></li>
            <li><a href="{{ url_for('main.projects') }}" class="text-white-50 text-decoration-none">Projects</a></li>
          </ul>
          {% else %}
          <h6 class="gold-text mb-3">روابط سريعة</h6>
          <ul class="list-unstyled">
            <li><a href="{{ url_for('main.home') }}" class="text-white-50 text-decoration-none">الرئيسية</a></li>
            <li><a href="{{ url_for('main.about') }}" class="text-white-50 text-decoration-none">من نحن</a></li>
            <li><a href="{{ url_for('main.services') }}" class="text-white-50 text-decoration-none">خدماتنا</a></li>
            <li><a href="{{ url_for('main.projects') }}" class="text-white-50 text-decoration-none">مشاريعنا</a></li>
          </ul>
          {% endif %}
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
          {% if current_lang == 'en' %}
          <h6 class="gold-text mb-3">Our Services</h6>
          <ul class="list-unstyled">
            <li class="text-white-50">Architectural Design</li>
            <li class="text-white-50">Supervision & Implementation</li>
            <li class="text-white-50">Renovation & Maintenance</li>
            <li class="text-white-50">Engineering Consultation</li>
          </ul>
          {% else %}
          <h6 class="gold-text mb-3">خدماتنا</h6>
          <ul class="list-unstyled">
            <li class="text-white-50">التصميم المعماري</li>
            <li class="text-white-50">الإشراف والتنفيذ</li>
            <li class="text-white-50">الترميم والصيانة</li>
            <li class="text-white-50">الاستشارات الهندسية</li>
          </ul>
          {% endif %}
        </div>

        <div class="col-lg-3 mb-4">
          {% if current_lang == 'en' %}
          <h6 class="gold-text mb-3">Contact Us</h6>
          <div class="text-white-50">
            <p><i class="fas fa-map-marker-alt me-2"></i>Misrata Al-Maqawba, Libya</p>
            <p><i class="fas fa-phone me-2"></i>945446851 218+</p>
            <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
          </div>
          {% else %}
          <h6 class="gold-text mb-3">تواصل معنا</h6>
          <div class="text-white-50">
            <p><i class="fas fa-map-marker-alt me-2"></i>مصراتة المقاوبة، ليبيا</p>
            <p><i class="fas fa-phone me-2"></i>945446851 218+</p>
            <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
          </div>
          {% endif %}
        </div>
      </div>

      <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

      <div class="row align-items-center">
        <div class="col-md-6">
          {% if current_lang == 'en' %}
          <p class="mb-0 text-white-50">© 2025 MYTHAQ Engineering Consultancy. All rights reserved.</p>
          {% else %}
          <p class="mb-0 text-white-50">© 2025 ميتاق للاستشارات الهندسية. جميع الحقوق محفوظة.</p>
          {% endif %}
        </div>
        <div class="col-md-6 text-md-end">
          {% if current_lang == 'en' %}
          <p class="mb-0 text-white-50">Design & Development: <span class="gold-text">MYTHAQ Tech Team</span></p>
          {% else %}
          <p class="mb-0 text-white-50">تصميم وتطوير: <span class="gold-text">فريق ميتاق التقني</span></p>
          {% endif %}
        </div>
      </div>
    </div>
  </footer>

  <!-- زر الواتساب العائم -->
  <div class="whatsapp-float">
    <a href="https://wa.me/218945446851" target="_blank" class="whatsapp-btn" title="{% if session.get('language', 'ar') == 'en' %}Chat on WhatsApp{% else %}تواصل عبر الواتساب{% endif %}">
      <i class="fab fa-whatsapp"></i>
      <span class="whatsapp-text">
        {% if session.get('language', 'ar') == 'en' %}
          WhatsApp
        {% else %}
          واتساب
        {% endif %}
      </span>
    </a>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

  <script>
    // Initialize AOS (Animate On Scroll)
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });



    // Navbar scroll effect
    window.addEventListener('scroll', function() {
      const navbar = document.getElementById('mainNavbar');
      if (navbar && window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else if (navbar) {
        navbar.classList.remove('scrolled');
      }
    });

    // Enhanced Button Functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Ensure all hero buttons are clickable and visible
      const heroButtons = document.querySelectorAll('.hero-section .btn, .hero-section .btn-lg');
      console.log('Found hero buttons:', heroButtons.length);

      heroButtons.forEach((button, index) => {
        console.log(`Button ${index}:`, button);
        // Force visibility and functionality
        button.style.pointerEvents = 'auto';
        button.style.cursor = 'pointer';
        button.style.zIndex = '1000';
        button.style.opacity = '1';
        button.style.visibility = 'visible';
        button.style.position = 'relative';

        // Add enhanced click effect
        button.addEventListener('click', function(e) {
          this.style.transform = 'scale(0.95)';
          setTimeout(() => {
            this.style.transform = '';
          }, 150);
        });

        // Enhanced hover effects
        button.addEventListener('mouseenter', function() {
          if (this.classList.contains('btn-gold')) {
            this.style.transform = 'translateY(-4px) scale(1.08)';
            this.style.boxShadow = '0 20px 40px rgba(212, 175, 55, 0.6)';
          } else if (this.classList.contains('btn-outline-light')) {
            this.style.transform = 'translateY(-4px) scale(1.08)';
            this.style.boxShadow = '0 20px 40px rgba(255, 255, 255, 0.3)';
          }
        });

        button.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
          if (this.classList.contains('btn-gold')) {
            this.style.boxShadow = '0 8px 25px rgba(212, 175, 55, 0.4)';
          } else {
            this.style.boxShadow = '';
          }
        });
      });

      // Fix any potential z-index issues
      const heroSection = document.querySelector('.hero-section');
      if (heroSection) {
        heroSection.style.position = 'relative';
        heroSection.style.zIndex = '1';
      }

      // Enhanced Language Button Functionality
      const languageBtn = document.getElementById('languageDropdown');
      if (languageBtn) {
        console.log('Language button found:', languageBtn);

        // Ensure button is visible and clickable
        languageBtn.style.display = 'inline-flex';
        languageBtn.style.visibility = 'visible';
        languageBtn.style.opacity = '1';
        languageBtn.style.pointerEvents = 'auto';
        languageBtn.style.zIndex = '1050';

        // Add click event for debugging
        languageBtn.addEventListener('click', function(e) {
          console.log('Language button clicked!');
        });

        // Add hover effects
        languageBtn.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        languageBtn.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      } else {
        console.log('Language button not found!');
      }

      // Ensure dropdown works
      const languageDropdown = document.querySelector('.language-dropdown');
      if (languageDropdown) {
        languageDropdown.style.zIndex = '1060';
      }
    });
  </script>

  {% block extra_js %}
  {% endblock %}
</body>
</html>
