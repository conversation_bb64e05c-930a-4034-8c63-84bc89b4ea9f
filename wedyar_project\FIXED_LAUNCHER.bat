@echo off
chcp 65001 >nul
title موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية

echo.
echo ================================================================
echo   🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo   🚀 MYTHAQ Engineering Works & Investments Group
echo   📍 إصلاح مشكلة المسار
echo   📍 Fixing Path Issue
echo ================================================================
echo.

REM تعيين المسار الصحيح
set "CORRECT_PATH=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\wedyar_project"

echo 📁 المسار الصحيح: %CORRECT_PATH%
echo 📁 Correct path: %CORRECT_PATH%
echo.

REM التحقق من وجود المجلد
if not exist "%CORRECT_PATH%" (
    echo ❌ المجلد غير موجود في المسار المحدد
    echo ❌ Folder not found at specified path
    echo.
    echo 🔍 البحث عن المجلد...
    echo 🔍 Searching for folder...
    
    REM البحث في مسارات محتملة أخرى
    for %%d in (
        "C:\Users\<USER>\Desktop\wedyar_project"
        "C:\Users\<USER>\Documents\wedyar_project"
        "C:\wedyar_project"
        "D:\wedyar_project"
    ) do (
        if exist "%%d" (
            set "CORRECT_PATH=%%d"
            echo ✅ تم العثور على المجلد في: %%d
            echo ✅ Found folder at: %%d
            goto :found
        )
    )
    
    echo ❌ لم يتم العثور على مجلد المشروع
    echo ❌ Project folder not found
    goto :error
)

:found
echo ✅ المجلد موجود
echo ✅ Folder exists

REM الانتقال إلى المجلد
cd /d "%CORRECT_PATH%"
if %errorlevel% neq 0 (
    echo ❌ فشل في الانتقال إلى المجلد
    echo ❌ Failed to change to folder
    goto :error
)

echo 📂 تم الانتقال إلى: %CD%
echo 📂 Changed to: %CD%
echo.

REM البحث عن ملفات HTML
echo 🔍 البحث عن ملفات الموقع...
echo 🔍 Looking for website files...

if exist "mythaq_final.html" (
    echo ✅ تم العثور على: mythaq_final.html
    echo ✅ Found: mythaq_final.html
    set "HTML_FILE=mythaq_final.html"
) else if exist "mythaq_website.html" (
    echo ✅ تم العثور على: mythaq_website.html
    echo ✅ Found: mythaq_website.html
    set "HTML_FILE=mythaq_website.html"
) else (
    echo ❌ لم يتم العثور على ملفات HTML
    echo ❌ HTML files not found
    echo.
    echo 📂 الملفات الموجودة:
    echo 📂 Available files:
    dir *.html /b 2>nul
    goto :error
)

echo.
echo 🌐 فتح الموقع...
echo 🌐 Opening website...

REM فتح الملف بطرق متعددة
start "" "%HTML_FILE%"
if %errorlevel% neq 0 (
    echo ⚠️ فشل الفتح بالطريقة الأولى، جرب طريقة أخرى...
    echo ⚠️ First method failed, trying another way...
    
    REM جرب مع المسار الكامل
    start "" "%CORRECT_PATH%\%HTML_FILE%"
    if %errorlevel% neq 0 (
        echo ⚠️ فشل الفتح بالطريقة الثانية، جرب explorer...
        echo ⚠️ Second method failed, trying explorer...
        explorer "%HTML_FILE%"
    )
)

echo.
echo ✅ تم تنفيذ أمر الفتح
echo ✅ Open command executed
echo.
echo 📋 معلومات الموقع:
echo 📋 Website information:
echo    - الشركة: مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo    - Company: MYTHAQ Engineering Works & Investments
echo    - المؤسس: م. علي عمر باكير ابورقيبه
echo    - Founder: Eng. Ali Omar Bakir Aburqaiba
echo    - الهاتف: +218 *********
echo    - البريد: <EMAIL>
echo.
echo 💡 إذا لم يفتح الموقع:
echo 💡 If website didn't open:
echo    1. افتح المتصفح يدوياً
echo    1. Open browser manually
echo    2. اسحب الملف: %CORRECT_PATH%\%HTML_FILE%
echo    2. Drag file: %CORRECT_PATH%\%HTML_FILE%
goto :end

:error
echo.
echo ❌ حدث خطأ في التشغيل
echo ❌ An error occurred
echo.
echo 💡 الحلول البديلة:
echo 💡 Alternative solutions:
echo    1. ابحث عن ملف mythaq_final.html يدوياً
echo    1. Search for mythaq_final.html manually
echo    2. انقر نقراً مزدوجاً على الملف
echo    2. Double-click on the file
echo    3. اسحب الملف إلى المتصفح
echo    3. Drag file to browser

:end
echo.
echo 🎉 انتهى التشغيل
echo 🎉 Execution completed
echo.
pause
