# PowerShell script to open MYTHAQ website
# سكريبت PowerShell لفتح موقع ميثاق

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "   🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية" -ForegroundColor Yellow
Write-Host "   🚀 MYTHAQ Engineering Works & Investments Group" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "📁 مجلد المشروع: $ScriptDir" -ForegroundColor Green
Write-Host "📁 Project folder: $ScriptDir" -ForegroundColor Green
Write-Host ""

# Look for HTML files
Write-Host "🔍 البحث عن ملفات الموقع..." -ForegroundColor Blue
Write-Host "🔍 Looking for website files..." -ForegroundColor Blue

$HtmlFile = $null

if (Test-Path "$ScriptDir\mythaq_final.html") {
    $HtmlFile = "$ScriptDir\mythaq_final.html"
    Write-Host "✅ تم العثور على: mythaq_final.html" -ForegroundColor Green
    Write-Host "✅ Found: mythaq_final.html" -ForegroundColor Green
}
elseif (Test-Path "$ScriptDir\mythaq_website.html") {
    $HtmlFile = "$ScriptDir\mythaq_website.html"
    Write-Host "✅ تم العثور على: mythaq_website.html" -ForegroundColor Green
    Write-Host "✅ Found: mythaq_website.html" -ForegroundColor Green
}
else {
    Write-Host "❌ لم يتم العثور على ملفات HTML" -ForegroundColor Red
    Write-Host "❌ HTML files not found" -ForegroundColor Red
    
    Write-Host ""
    Write-Host "📂 الملفات الموجودة:" -ForegroundColor Yellow
    Write-Host "📂 Available files:" -ForegroundColor Yellow
    Get-ChildItem "$ScriptDir\*.html" -ErrorAction SilentlyContinue | ForEach-Object { Write-Host "   $($_.Name)" }
    
    Write-Host ""
    Read-Host "اضغط Enter للخروج / Press Enter to exit"
    exit
}

Write-Host ""
Write-Host "🌐 فتح الموقع..." -ForegroundColor Blue
Write-Host "🌐 Opening website..." -ForegroundColor Blue
Write-Host "📂 الملف: $HtmlFile" -ForegroundColor Gray
Write-Host "📂 File: $HtmlFile" -ForegroundColor Gray
Write-Host ""

try {
    # Try to open with default browser
    Start-Process $HtmlFile
    Write-Host "✅ تم فتح الموقع بنجاح!" -ForegroundColor Green
    Write-Host "✅ Website opened successfully!" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ خطأ في فتح الملف: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "⚠️ Error opening file: $($_.Exception.Message)" -ForegroundColor Yellow
    
    Write-Host ""
    Write-Host "💡 يمكنك فتح الملف يدوياً:" -ForegroundColor Cyan
    Write-Host "💡 You can open the file manually:" -ForegroundColor Cyan
    Write-Host "   $HtmlFile" -ForegroundColor White
}

Write-Host ""
Write-Host "📋 معلومات الموقع:" -ForegroundColor Cyan
Write-Host "📋 Website information:" -ForegroundColor Cyan
Write-Host "   - الشركة: مجموعة ميثاق للأعمال والاستثمارات الهندسية" -ForegroundColor White
Write-Host "   - Company: MYTHAQ Engineering Works & Investments" -ForegroundColor White
Write-Host "   - المؤسس: م. علي عمر باكير ابورقيبه" -ForegroundColor White
Write-Host "   - Founder: Eng. Ali Omar Bakir Aburqaiba" -ForegroundColor White
Write-Host "   - الهاتف: +218 *********" -ForegroundColor White
Write-Host "   - البريد: <EMAIL>" -ForegroundColor White

Write-Host ""
Write-Host "🎉 انتهى التشغيل" -ForegroundColor Green
Write-Host "🎉 Execution completed" -ForegroundColor Green
Write-Host ""
Read-Host "اضغط Enter للخروج / Press Enter to exit"
