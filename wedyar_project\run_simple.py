import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from flask_bootstrap import Bootstrap

def create_simple_app():
    app = Flask(__name__)
    app.config["SECRET_KEY"] = "your_secret_key_here"
    app.config['LANGUAGES'] = {
        'ar': 'العربية',
        'en': 'English'
    }
    
    Bootstrap(app)
    
    from app.routes_simple import main
    app.register_blueprint(main)
    
    return app

if __name__ == "__main__":
    print("🚀 بدء تشغيل موقع MYTHAQ (النسخة المبسطة)...")
    print("📍 الموقع متاح على: http://127.0.0.1:5000")
    print("⏹️  للإيقاف اضغط Ctrl+C")
    print("-" * 50)

    try:
        app = create_simple_app()
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=True
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")
