#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم ويب بسيط لموقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
Simple web server for MYTHAQ Engineering Works & Investments
يعمل بدون مكتبات خارجية - يستخدم فقط مكتبات Python الأساسية
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import threading
import time
from urllib.parse import urlparse, parse_qs

# إعداد المنفذ
PORT = 8000

class MythaqWebHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        """معالجة طلبات GET"""
        
        # تحليل المسار
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # الصفحة الرئيسية
        if path == '/' or path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            # قراءة ملف HTML الجاهز
            try:
                with open('mythaq_website.html', 'r', encoding='utf-8') as f:
                    html_content = f.read()
                self.wfile.write(html_content.encode('utf-8'))
            except FileNotFoundError:
                # إذا لم يوجد الملف، أنشئ صفحة بسيطة
                html_content = self.get_fallback_page()
                self.wfile.write(html_content.encode('utf-8'))
            
        else:
            # ملفات أخرى
            super().do_GET()
    
    def get_fallback_page(self):
        """صفحة احتياطية في حالة عدم وجود الملف الأساسي"""
        return '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجموعة ميثاق للأعمال والاستثمارات الهندسية</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        .gold {
            color: #CBA135;
        }
        
        .hero {
            padding: 60px 0;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero h2 {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .status {
            background: rgba(40,167,69,0.2);
            padding: 30px;
            border-radius: 15px;
            margin: 40px 0;
            border: 2px solid #28a745;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .info-card {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(203,161,53,0.3);
        }
        
        .info-card h3 {
            color: #CBA135;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        .info-card p, .info-card li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .info-card ul {
            text-align: right;
            padding-right: 20px;
        }
        
        .whatsapp-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #25D366;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            text-decoration: none;
            box-shadow: 0 4px 12px rgba(37,211,102,0.3);
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .whatsapp-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37,211,102,0.4);
        }
        
        .footer {
            margin-top: 60px;
            padding: 30px 0;
            border-top: 1px solid rgba(255,255,255,0.2);
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1><span class="gold">ميثاق</span></h1>
            <h2>مجموعة ميثاق للأعمال والاستثمارات الهندسية</h2>
            <h2 style="font-size: 1.2rem;">MYTHAQ Engineering Works & Investments</h2>
        </div>
        
        <div class="status">
            <h2>🎉 الموقع يعمل بنجاح!</h2>
            <h3>Website is running successfully!</h3>
            <p>الخادم يعمل على المنفذ ''' + str(PORT) + '''</p>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>👤 المؤسس / Founder</h3>
                <p><strong>م. علي عمر باكير ابورقيبه</strong></p>
                <p>Eng. Ali Omar Bakir Aburqaiba</p>
                <p>المؤسس والمدير التنفيذي</p>
                <p>Founder & CEO</p>
            </div>
            
            <div class="info-card">
                <h3>📍 معلومات الاتصال / Contact Info</h3>
                <p><strong>العنوان:</strong> ليبيا، مصراتة، المقاوبة</p>
                <p><strong>الهاتف:</strong> +218 945446851</p>
                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p><strong>ساعات العمل:</strong> الأحد - الخميس: 8:00 ص - 6:00 م</p>
            </div>
            
            <div class="info-card">
                <h3>🏢 خدماتنا / Our Services</h3>
                <ul>
                    <li>التصميم المعماري والداخلي</li>
                    <li>الإشراف والتنفيذ</li>
                    <li>الترميم والصيانة</li>
                    <li>الاستشارات الهندسية والاستثمارية</li>
                    <li>تطوير المشاريع السكنية والتجارية</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🎯 رسالتنا / Our Mission</h3>
                <p>أن نكون المجموعة الرائدة في الأعمال والاستثمارات الهندسية في ليبيا، نقدم حلولاً مبتكرة تفوق التوقعات وتساهم في بناء مستقبل أفضل لمجتمعنا.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 مجموعة ميثاق للأعمال والاستثمارات الهندسية - جميع الحقوق محفوظة</p>
            <p>MYTHAQ Engineering Works & Investments Group - All Rights Reserved</p>
        </div>
    </div>
    
    <a href="https://wa.me/218945446851" target="_blank" class="whatsapp-btn">
        💬 واتساب
    </a>
</body>
</html>'''

def open_browser_delayed():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(2)
    try:
        webbrowser.open(f'http://localhost:{PORT}')
        print("🌐 تم فتح المتصفح تلقائياً / Browser opened automatically")
    except:
        print("⚠️ لم يتم فتح المتصفح تلقائياً / Browser not opened automatically")

def main():
    """تشغيل الخادم"""
    
    print("🚀 بدء تشغيل موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية")
    print("🚀 Starting MYTHAQ Engineering Works & Investments website")
    print("=" * 70)
    print(f"📍 الموقع متاح على: http://localhost:{PORT}")
    print(f"🌐 Website available at: http://localhost:{PORT}")
    print("⏹️  للإيقاف اضغط Ctrl+C / Press Ctrl+C to stop")
    print("-" * 70)
    
    try:
        # تغيير المجلد الحالي إلى مجلد المشروع
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # إنشاء الخادم
        with socketserver.TCPServer(("", PORT), MythaqWebHandler) as httpd:
            print(f"✅ الخادم يعمل على المنفذ {PORT}")
            print(f"✅ Server running on port {PORT}")
            print("\n🎉 الموقع جاهز! / Website is ready!")
            print("📱 يمكنك الآن زيارة الموقع / You can now visit the website")
            print("🔄 الخادم يعمل بدون مكتبات خارجية / Server running without external libraries")
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🔴 تم إيقاف الخادم / Server stopped")
        print("👋 شكراً لاستخدام موقع ميثاق / Thank you for using MYTHAQ website")
        
    except OSError as e:
        if e.errno == 10048:  # Address already in use
            print(f"❌ المنفذ {PORT} مستخدم بالفعل / Port {PORT} is already in use")
            print("💡 جرب إغلاق البرامج الأخرى أو استخدم منفذ آخر")
            print("💡 Try closing other programs or use a different port")
        else:
            print(f"❌ خطأ في الشبكة / Network error: {e}")
            
    except Exception as e:
        print(f"❌ خطأ غير متوقع / Unexpected error: {e}")
        
    finally:
        input("\nاضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
