@echo off
chcp 65001 >nul
title موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية

echo.
echo ========================================
echo   🚀 تشغيل موقع ميثاق
echo   MYTHAQ Engineering Works & Investments
echo ========================================
echo.

cd /d "%~dp0"

echo 📍 التحقق من Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo.
echo 📦 التحقق من Flask...
python -c "import flask; print('✅ Flask متاح - الإصدار:', flask.__version__)" 2>nul
if errorlevel 1 (
    echo ⚠️ Flask غير مثبت. جاري التثبيت...
    pip install flask flask-bootstrap4
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
)

echo.
echo 🚀 بدء تشغيل الخادم...
echo 📍 الموقع سيكون متاحاً على: http://127.0.0.1:5000
echo ⏹️  للإيقاف اضغط Ctrl+C
echo.

python simple_run.py

echo.
echo 🔴 تم إيقاف الخادم
pause
