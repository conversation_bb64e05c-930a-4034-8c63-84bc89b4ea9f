@echo off
chcp 65001 >nul
title موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية - MYTHAQ

echo.
echo ================================================================
echo   🚀 موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
echo   🚀 MYTHAQ Engineering Works & Investments Group
echo   📍 خادم ويب مستقل - لا يحتاج مكتبات خارجية
echo   📍 Standalone Web Server - No External Libraries Required
echo ================================================================
echo.

cd /d "%~dp0"

echo 🔍 البحث عن Python...
echo 🔍 Looking for Python...

REM جرب python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo ✅ Python found
    echo.
    echo 🚀 تشغيل الخادم باستخدام python...
    echo 🚀 Starting server with python...
    python standalone_server.py
    goto :end
)

REM جرب py
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python (py)
    echo ✅ Python found (py)
    echo.
    echo 🚀 تشغيل الخادم باستخدام py...
    echo 🚀 Starting server with py...
    py standalone_server.py
    goto :end
)

REM جرب python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python3
    echo ✅ Python3 found
    echo.
    echo 🚀 تشغيل الخادم باستخدام python3...
    echo 🚀 Starting server with python3...
    python3 standalone_server.py
    goto :end
)

REM إذا لم يتم العثور على Python
echo ❌ لم يتم العثور على Python
echo ❌ Python not found
echo.
echo 💡 يرجى تثبيت Python من:
echo 💡 Please install Python from:
echo    https://python.org
echo.
echo 📋 تعليمات التثبيت:
echo 📋 Installation instructions:
echo    1. قم بتحميل Python من الرابط أعلاه
echo    1. Download Python from the link above
echo    2. أثناء التثبيت، تأكد من تحديد "Add Python to PATH"
echo    2. During installation, make sure to check "Add Python to PATH"
echo    3. أعد تشغيل هذا الملف بعد التثبيت
echo    3. Run this file again after installation
echo.

:end
echo.
echo 🔴 تم إنهاء البرنامج
echo 🔴 Program ended
echo.
pause
