#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم ويب محسن لموقع مجموعة ميثاق للأعمال والاستثمارات الهندسية
Enhanced web server for MYTHAQ Engineering Works & Investments
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import threading
import time
from urllib.parse import urlparse

# إعداد المنفذ
PORT = 8000

class MythaqHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def do_GET(self):
        """معالجة طلبات GET"""
        print(f"📥 طلب GET: {self.path}")
        
        # تحليل المسار
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # الصفحة الرئيسية
        if path == '/' or path == '/index.html':
            self.serve_main_page()
        else:
            # ملفات أخرى
            super().do_GET()
    
    def serve_main_page(self):
        """تقديم الصفحة الرئيسية"""
        try:
            # محاولة قراءة ملف HTML الجاهز
            html_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mythaq_website.html')
            
            if os.path.exists(html_file):
                print(f"📄 تحميل ملف: {html_file}")
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
            else:
                print("⚠️ ملف HTML غير موجود، استخدام الصفحة الاحتياطية")
                html_content = self.get_fallback_page()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Content-Length', len(html_content.encode('utf-8')))
            self.end_headers()
            self.wfile.write(html_content.encode('utf-8'))
            
            print("✅ تم إرسال الصفحة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في تقديم الصفحة: {e}")
            self.send_error(500, f"خطأ داخلي: {e}")
    
    def get_fallback_page(self):
        """صفحة احتياطية"""
        return '''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجموعة ميثاق للأعمال والاستثمارات الهندسية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }
        .gold { color: #CBA135; }
        .hero {
            padding: 40px 0;
        }
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .status {
            background: rgba(40,167,69,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            border: 2px solid #28a745;
        }
        .info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }
        .whatsapp {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #25D366;
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1><span class="gold">ميثاق</span></h1>
            <h2>مجموعة ميثاق للأعمال والاستثمارات الهندسية</h2>
            <h3>MYTHAQ Engineering Works & Investments</h3>
        </div>
        
        <div class="status">
            <h2>🎉 الخادم يعمل بنجاح!</h2>
            <p>Server is running successfully on port ''' + str(PORT) + '''</p>
        </div>
        
        <div class="info">
            <h3 class="gold">👤 المؤسس</h3>
            <p><strong>م. علي عمر باكير ابورقيبه</strong></p>
            <p>Eng. Ali Omar Bakir Aburqaiba</p>
            <p>المؤسس والمدير التنفيذي</p>
        </div>
        
        <div class="info">
            <h3 class="gold">📞 معلومات الاتصال</h3>
            <p><strong>الهاتف:</strong> +218 945446851</p>
            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
            <p><strong>العنوان:</strong> ليبيا، مصراتة، المقاوبة</p>
        </div>
        
        <div class="info">
            <h3 class="gold">🏢 خدماتنا</h3>
            <ul style="text-align: right; padding-right: 20px;">
                <li>التصميم المعماري والداخلي</li>
                <li>الإشراف والتنفيذ</li>
                <li>الترميم والصيانة</li>
                <li>الاستشارات الهندسية والاستثمارية</li>
                <li>تطوير المشاريع السكنية والتجارية</li>
            </ul>
        </div>
    </div>
    
    <a href="https://wa.me/218945446851" target="_blank" class="whatsapp">
        💬 واتساب
    </a>
</body>
</html>'''

    def log_message(self, format, *args):
        """تسجيل الرسائل"""
        print(f"🌐 {self.address_string()} - {format % args}")

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        url = f'http://localhost:{PORT}'
        print(f"🌐 فتح المتصفح: {url}")
        webbrowser.open(url)
        print("✅ تم فتح المتصفح بنجاح")
    except Exception as e:
        print(f"⚠️ لم يتم فتح المتصفح: {e}")

def main():
    """تشغيل الخادم"""
    
    print("🚀 بدء تشغيل موقع مجموعة ميثاق للأعمال والاستثمارات الهندسية")
    print("🚀 Starting MYTHAQ Engineering Works & Investments website")
    print("=" * 70)
    
    # تغيير المجلد الحالي
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 مجلد العمل: {script_dir}")
    
    try:
        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # إنشاء الخادم
        with socketserver.TCPServer(("", PORT), MythaqHandler) as httpd:
            print(f"✅ الخادم يعمل على المنفذ {PORT}")
            print(f"📍 الموقع متاح على: http://localhost:{PORT}")
            print("⏹️  للإيقاف اضغط Ctrl+C")
            print("-" * 70)
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🔴 تم إيقاف الخادم")
        
    except OSError as e:
        if e.errno == 10048:
            print(f"❌ المنفذ {PORT} مستخدم بالفعل")
            print("💡 جرب إغلاق البرامج الأخرى أو استخدم منفذ آخر")
        else:
            print(f"❌ خطأ في الشبكة: {e}")
            
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("👋 شكراً لاستخدام موقع ميثاق")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
