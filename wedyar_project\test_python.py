#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 اختبار Python")
print("🚀 Python Test")
print("=" * 50)

try:
    import sys
    print(f"✅ Python version: {sys.version}")
    
    import os
    print(f"✅ Current directory: {os.getcwd()}")
    
    import http.server
    print("✅ http.server module available")
    
    import socketserver
    print("✅ socketserver module available")
    
    import webbrowser
    print("✅ webbrowser module available")
    
    print("\n🎉 جميع المكتبات متاحة!")
    print("🎉 All modules available!")
    
    # اختبار بسيط للخادم
    PORT = 8001
    
    class SimpleHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = '''<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار ميثاق</title>
    <style>
        body { 
            font-family: Arial; 
            background: linear-gradient(135deg, #1e3c72, #2a5298); 
            color: white; 
            text-align: center; 
            padding: 50px; 
        }
        .gold { color: #CBA135; }
    </style>
</head>
<body>
    <h1 class="gold">ميثاق</h1>
    <h2>مجموعة ميثاق للأعمال والاستثمارات الهندسية</h2>
    <h3>MYTHAQ Engineering Works & Investments</h3>
    <p>🎉 الخادم يعمل بنجاح على المنفذ ''' + str(PORT) + '''!</p>
    <p>✅ Server running successfully on port ''' + str(PORT) + '''!</p>
    <hr>
    <p><strong>المؤسس:</strong> م. علي عمر باكير ابورقيبه</p>
    <p><strong>الهاتف:</strong> +218 945446851</p>
    <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
</body>
</html>'''
            
            self.wfile.write(html.encode('utf-8'))
    
    print(f"\n🚀 بدء تشغيل خادم اختبار على المنفذ {PORT}")
    print(f"🚀 Starting test server on port {PORT}")
    print(f"📍 الموقع: http://localhost:{PORT}")
    
    with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
        print("✅ الخادم يعمل!")
        print("✅ Server running!")
        print("⏹️ اضغط Ctrl+C للإيقاف")
        
        # فتح المتصفح
        import threading
        import time
        
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 تم فتح المتصفح")
            except:
                print("⚠️ لم يتم فتح المتصفح")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        httpd.serve_forever()

except ImportError as e:
    print(f"❌ مكتبة مفقودة: {e}")
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
